"""测试重构后的功能"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """测试所有模块导入"""
    try:
        from src.models.elements import TextBox, ImageBox, BarcodeBox
        print("✓ 模型模块导入成功")
        
        from src.utils.constants import CHINESE_FONT_SIZES, A4_WIDTH_MM, A4_HEIGHT_MM
        print("✓ 常量模块导入成功")
        
        from src.utils.font_utils import setup_fonts, get_font_path
        print("✓ 字体工具模块导入成功")
        
        from src.utils.tk_utils import fix_tcl_tk_environment, check_imagetk_availability
        print("✓ Tkinter工具模块导入成功")
        
        from src.core.excel_handler import ExcelHandler
        print("✓ Excel处理模块导入成功")
        
        from src.core.image_handler import ImageHandler
        print("✓ 图片处理模块导入成功")
        
        from src.core.pdf_generator import PDFGenerator
        print("✓ PDF生成模块导入成功")
        
        from src.ui.main_window import PDFGeneratorApp
        print("✓ UI模块导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_models():
    """测试数据模型"""
    try:
        from src.models.elements import TextBox, ImageBox, BarcodeBox
        
        # 测试TextBox
        textbox = TextBox(10, 20, text="测试文本")
        assert textbox.x == 10
        assert textbox.y == 20
        assert textbox.text == "测试文本"
        assert textbox.element_type == "text"
        print("✓ TextBox模型测试通过")
        
        # 测试ImageBox
        imagebox = ImageBox(30, 40, ksh_placeholder="{学号}")
        assert imagebox.x == 30
        assert imagebox.y == 40
        assert imagebox.ksh_placeholder == "{学号}"
        assert imagebox.element_type == "image"
        print("✓ ImageBox模型测试通过")
        
        # 测试BarcodeBox
        barcodebox = BarcodeBox(50, 60, text="{条形码}")
        assert barcodebox.x == 50
        assert barcodebox.y == 60
        assert barcodebox.text == "{条形码}"
        assert barcodebox.element_type == "barcode"
        print("✓ BarcodeBox模型测试通过")
        
        return True
    except Exception as e:
        print(f"✗ 数据模型测试失败: {e}")
        return False

def test_handlers():
    """测试处理器"""
    try:
        from src.core.excel_handler import ExcelHandler
        from src.core.image_handler import ImageHandler
        from src.core.pdf_generator import PDFGenerator
        
        # 测试ExcelHandler
        excel_handler = ExcelHandler()
        assert excel_handler.excel_data is None
        print("✓ ExcelHandler初始化成功")
        
        # 测试ImageHandler
        assert hasattr(ImageHandler, 'find_image_by_ksh')
        assert hasattr(ImageHandler, 'get_image_dpi')
        print("✓ ImageHandler静态方法存在")
        
        # 测试PDFGenerator
        pdf_gen = PDFGenerator()
        assert hasattr(pdf_gen, 'generate_pdf_page')
        assert hasattr(pdf_gen, 'generate_batch_pdf')
        print("✓ PDFGenerator初始化成功")
        
        return True
    except Exception as e:
        print(f"✗ 处理器测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始测试重构后的功能...")
    print()
    
    tests = [
        ("模块导入测试", test_imports),
        ("数据模型测试", test_data_models),
        ("处理器测试", test_handlers),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"运行 {test_name}...")
        if test_func():
            passed += 1
            print("✓ 通过")
        else:
            failed += 1
            print("✗ 失败")
        print()
    
    print(f"测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！重构成功！")
        return True
    else:
        print("❌ 部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)