"""PDF批量生成器 - 重构版本入口

功能：
1. 显示A4横向画布（297×210mm），根据屏幕DPI等比缩放
2. 加载背景图片，等比例铺满整个画布
3. 导入Excel数据，支持占位符系统
4. 添加文本框，支持拖拽、编辑、占位符
5. 预览PDF（第一条数据）
6. 批量导出PDF（所有数据）
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 修复Tcl/Tk环境变量问题
from src.utils.tk_utils import fix_tcl_tk_environment
fix_tcl_tk_environment()

# 设置CustomTkinter外观
import customtkinter as ctk
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

from src.ui import PDFGeneratorApp


def main():
    """主程序入口"""
    try:
        app = PDFGeneratorApp()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()