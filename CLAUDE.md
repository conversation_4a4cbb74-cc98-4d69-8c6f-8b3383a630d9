# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a mature Python GUI application for PDF batch generation, built with CustomTkinter. The application provides a visual document designer where users can create PDF templates using text fields, barcodes, and images, then batch generate PDFs using data from Excel files. The core workflow is: import Excel data → design template with elements → generate batch PDFs.

## Development Commands

### Dependency Management
- `uv sync` - Install dependencies and sync lockfile
- `uv add <package>` - Add a new dependency
- `uv remove <package>` - Remove a dependency

### Running the Application
- `uv run python main.py` - Run the main application
- `uv run main.py` - Alternative way to run (if configured as script)

### Development Environment
- Requires Python >= 3.12
- Uses `uv` for dependency management (modern Python package manager)
- Virtual environment is automatically managed by `uv`

## Architecture Overview

### Core Design Pattern
The application follows a monolithic architecture with all components in a single `main.py` file. The main classes are:

- **PDFGenerator**: Main application class containing all GUI setup and business logic
- **TextBox**: Data class for text elements with positioning, font, and content properties
- **ImageBox**: Data class for image elements with ksh placeholder system for dynamic image loading
- **BarcodeBox**: Data class for barcode elements with configurable types and dimensions

### Key Components Architecture

#### Element System
- **Element Classes**: `TextBox`, `BarcodeBox`, `ImageBox` - data classes representing design elements
- **Element Properties**: All elements have `x`, `y`, `width`, `height` (in mm), `selected` state, and `canvas_ids` list
- **Coordinate System**: All positions/sizes stored in millimeters, converted to pixels for display using dynamic scaling factors

#### Canvas & Preview System
- **Dynamic Scaling**: Canvas adapts to background image DPI and physical dimensions using `scale_x/scale_y` factors
- **1:1 Display**: Font sizes, barcode modules, and image elements scale proportionally to maintain real-world proportions
- **Scrollable Canvas**: Automatically adds scrollbars for large background images
- **Drag & Drop**: Real-time element positioning with boundary constraints

#### Data Flow
1. **Excel Import** → `load_excel()` reads data with pandas → `update_excel_preview()` shows column info
2. **Design Phase** → Elements positioned on canvas → Properties updated via right panel
3. **Template System** → No persistent template saving (single session only)
4. **PDF Generation** → `generate_batch_pdf()` processes each Excel row → Batch output to `output/` folder

## Critical Implementation Details

### Coordinate System & Scaling
- **Base Units**: All element positions/dimensions stored in millimeters (A4 landscape: 297×210mm)
- **Dynamic Scaling**: `scale_x = canvas_pixels / A4_WIDTH_MM` - calculated per background image DPI
- **Font Scaling**: Uses `font_size_pt * display_dpi / 72 * scale_factor` for 1:1 display
- **Image Scaling**: Background images scale based on DPI metadata, fallback to pixel-based scaling

### Image Processing Pipeline
- **Multi-format Support**: JPG/PNG/GIF with comprehensive PIL compatibility handling
- **DPI Intelligence**: Reads image DPI metadata, validates range (50-600 DPI), falls back to pixel scaling
- **Canvas Adaptation**: Canvas dynamically resizes to fit scaled background images without cropping
- **Error Recovery**: 3-tier fallback system (direct conversion → PNG buffer → temp file)

### Element Rendering System
- **Text Elements**: Support Chinese font sizes (初号-小六), placeholder replacement with Excel data preview
- **Barcode Elements**: Module width/height scale with canvas factor, supports Code128/39/EAN13/8
- **Image Elements**: Maintain aspect ratio, center within element bounds, real image preview when available

### State Management
- **Element Selection**: Single selected element with visual highlighting and property panel binding  
- **Drag Operations**: 3-pixel threshold, boundary constraints, real-time coordinate feedback
- **Template Persistence**: JSON-based save/load with element serialization and file path references

## Key Dependencies & Integration Points

### External Libraries
- **CustomTkinter**: Modern UI framework (≥5.2.2) - provides styled widgets and dark theme
- **ReportLab**: PDF generation engine (≥4.4.2) - handles document creation and font rendering
- **Pillow**: Image processing (≥11.3.0) - crucial for background image handling and format conversion
- **python-barcode**: Barcode generation (≥0.15.1) - creates Code128/39/EAN barcodes with configurable modules
- **pandas + openpyxl**: Excel processing (≥2.3.1, ≥3.1.5) - data import and validation

### Font System Integration
- **Cross-platform Detection**: Searches system font directories (Windows/macOS/Linux) in `setup_fonts()`
- **Font Registration**: Attempts to register SimHei/STHeiti fonts with ReportLab for Chinese text support
- **Fallback Handling**: Uses Helvetica if no suitable Chinese fonts are found

### Threading Model
- **PDF Generation**: Synchronous on main thread (no background threading implemented)
- **Image Loading**: Synchronous with comprehensive error handling and 3-tier fallback system
- **File Operations**: Excel/template loading handled synchronously on main thread with user feedback

## Development Guidelines

### Working with Elements
When adding new element types, follow the established pattern:
1. Create element data class in `main.py` with required properties (`x`, `y`, `width`, `height`, `selected`, `canvas_ids`, `element_type`)
2. Add corresponding draw method like `draw_textbox()`, `draw_imagebox()`, `draw_barcodebox()`
3. Update `select_element()` method to handle the new element type
4. Add UI controls in `setup_property_panel()` and update methods in `update_property_panel()`
5. Update canvas click detection in `on_canvas_click()` and drag handling in `on_canvas_drag()`

### Coordinate System Rules
- Store all dimensions in millimeters for consistency
- Use `self.scale_x/scale_y` for pixel conversion in rendering (calculated in `setup_canvas_scaling()`)
- Maintain aspect ratios when scaling images/barcodes
- Test with different DPI background images to verify scaling
- A4 landscape dimensions: 297×210mm

### Image Processing Best Practices  
- Handle PIL version compatibility (use try/except for new vs old APIs like `Image.Resampling.LANCZOS`)
- Convert images to RGB mode before processing for PDF generation
- Implement 3-tier fallback system: ImageTk → PPM format → placeholder display
- Cache image references in element objects to prevent garbage collection

### Canvas Management
- Clear individual elements using `canvas_ids` list before redraw
- Cache image/barcode references in element instances to prevent garbage collection  
- Use element-specific tags like `f"textbox_{id(textbox)}"` for event binding
- Background images use "background" tag and are placed at bottom layer

### Common Troubleshooting Areas
- **Font rendering**: Check system font paths and ReportLab registration in `setup_fonts()`
- **DPI scaling**: Verify background image metadata and scaling factor calculations in `setup_canvas_scaling()`
- **Excel compatibility**: Test with both .xlsx and .xls formats - uses pandas for reading
- **Image compatibility**: PIL/ImageTk compatibility issues - application has 3-tier fallback system
- **Memory usage**: Large background images can cause performance issues - images are cached in element objects
- **Tcl/Tk environment**: The `fix_tcl_tk_environment()` function handles uv virtual environment Tcl/Tk path issues

## Project Structure

### File Organization
```
admission_generation/
├── main.py                    # Main application file (entire codebase)
├── pyproject.toml            # UV dependency configuration
├── uv.lock                   # Dependency lockfile
├── README.md                 # Project documentation (Chinese)
├── KSZP/                     # Student photos directory (*.jpg files)
├── output/                   # Generated PDF output directory
└── *.xlsx                    # Excel data files for batch processing
```

### Key File Locations
- **Main Application**: `main.py:104-1821` - Complete GUI application
- **Element Classes**: `main.py:104-150` - TextBox, ImageBox, BarcodeBox data classes  
- **PDF Generation**: `main.py:1461-1803` - Single page and batch PDF generation methods
- **Image Processing**: `main.py:1032-1100` - Image loading with ksh placeholder system
- **Excel Integration**: `main.py:536-570` - Excel data loading and preview