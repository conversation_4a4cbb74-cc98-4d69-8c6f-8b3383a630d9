"""字体相关工具"""

import os
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont


def setup_fonts():
    """设置字体"""
    # 尝试注册黑体字体
    font_paths = [
        "/System/Library/Fonts/STHeiti Light.ttc",  # macOS
        "/System/Library/Fonts/Helvetica.ttc",  # macOS备选
        "C:/Windows/Fonts/simhei.ttf",  # Windows
        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
    ]

    font_registered = False
    for font_path in font_paths:
        if os.path.exists(font_path):
            try:
                pdfmetrics.registerFont(TTFont("SimHei", font_path))
                font_registered = True
                print(f"已注册字体: {font_path}")
                break
            except:
                continue

    if not font_registered:
        print("警告: 未找到合适的中文字体，将使用默认字体")
    
    return font_registered


def get_font_path():
    """获取黑体字体路径，用于条形码文字"""
    font_paths = [
        "/System/Library/Fonts/STHeiti Light.ttc",  # macOS黑体
        "/System/Library/Fonts/PingFang.ttc",      # macOS苹方字体
        "C:/Windows/Fonts/simhei.ttf",              # Windows黑体
        "C:/Windows/Fonts/msyh.ttc",                # Windows微软雅黑
        "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",  # Linux粗体
        "/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf",  # Linux Liberation粗体
    ]
    
    for font_path in font_paths:
        if os.path.exists(font_path):
            return font_path
    
    # 如果没有找到合适的字体，返回None使用默认字体
    return None