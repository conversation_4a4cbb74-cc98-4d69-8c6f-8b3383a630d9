"""Tkinter环境修复工具"""

import os
import sys
import glob
from pathlib import Path


def fix_tcl_tk_environment():
    """修复uv虚拟环境中的Tcl/Tk配置问题"""
    try:
        # 查找Tcl库
        tcl_init_files = glob.glob(os.path.join(sys.base_prefix, "lib", "tcl*", "init.tcl"))
        if tcl_init_files:
            os.environ["TCL_LIBRARY"] = str(Path(tcl_init_files[0]).parent)

        # 查找Tk库
        tk_files = glob.glob(os.path.join(sys.base_prefix, "lib", "tk*"))
        if tk_files:
            os.environ["TK_LIBRARY"] = tk_files[0]

        # 如果上面的方法失败，尝试更通用的路径
        if "TCL_LIBRARY" not in os.environ:
            # 对于uv管理的Python环境
            python_lib_path = os.path.join(sys.base_prefix, "lib")
            if os.path.exists(python_lib_path):
                tcl_dirs = glob.glob(os.path.join(python_lib_path, "tcl*"))
                tk_dirs = glob.glob(os.path.join(python_lib_path, "tk*"))

                if tcl_dirs:
                    os.environ["TCL_LIBRARY"] = tcl_dirs[0]
                if tk_dirs:
                    os.environ["TK_LIBRARY"] = tk_dirs[0]
    except Exception as e:
        print(f"警告: 无法自动配置Tcl/Tk环境: {e}")


def check_imagetk_availability():
    """检查ImageTk可用性"""
    try:
        from PIL import ImageTk
        return True, ImageTk
    except (ImportError, TypeError) as e:
        print(f"警告: ImageTk不可用 ({e})，将使用替代显示方案")
        return False, None