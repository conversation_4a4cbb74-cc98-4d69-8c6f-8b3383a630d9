"""常量定义模块"""

# 中文字号对应磅值表
CHINESE_FONT_SIZES = {
    "初号": 42,
    "小初": 36,
    "一号": 26,
    "小一": 24,
    "二号": 22,
    "小二": 18,
    "三号": 16,
    "小三": 15,
    "四号": 14,
    "小四": 12,
    "五号": 10.5,
    "小五": 9,
    "六号": 7.5,
    "小六": 6.5,
}

# 反向映射：磅值到中文字号
POINT_TO_CHINESE = {v: k for k, v in CHINESE_FONT_SIZES.items()}

# A4尺寸相关常量
A4_WIDTH_MM = 297  # A4横向宽度
A4_HEIGHT_MM = 210  # A4横向高度

# 条形码类型
BARCODE_TYPES = ["code128", "code39", "ean13", "ean8"]

# 默认DPI设置
DEFAULT_DPI = 300
MIN_DPI = 50
MAX_DPI = 600