"""元素数据模型"""

from ..utils.constants import CHINESE_FONT_SIZES


class TextBox:
    """文本框类"""

    def __init__(self, x, y, width=100, height=30, text="文本框", font_size_name="小四", line_height=1.2):
        self.x = x  # 毫米
        self.y = y  # 毫米
        self.width = width  # 毫米
        self.height = height  # 毫米
        self.text = text
        self.font_size_name = font_size_name  # 中文字号名称
        self.font_size = CHINESE_FONT_SIZES.get(font_size_name, 12)  # 对应的磅值
        self.line_height = line_height
        self.selected = False
        self.canvas_ids = []  # 改为列表存储多个canvas对象
        self.element_type = "text"  # 元素类型


class ImageBox:
    """图片框类"""

    def __init__(self, x, y, width=30, height=40, ksh_placeholder="{ksh}"):
        self.x = x  # 毫米
        self.y = y  # 毫米
        self.width = width  # 毫米
        self.height = height  # 毫米
        self.ksh_placeholder = ksh_placeholder  # ksh占位符
        self.selected = False
        self.canvas_ids = []  # 存储canvas对象
        self.element_type = "image"  # 元素类型
        self.image_data = None  # 缓存的图片数据


class BarcodeBox:
    """条形码框类"""

    def __init__(self, x, y, width=60, height=16, text="{ksh}", barcode_type="code128"):
        self.x = x  # 毫米
        self.y = y  # 毫米
        self.width = width  # 毫米（15:4比例，默认60mm宽）
        self.height = height  # 毫米（15:4比例，默认16mm高）
        self.text = text  # 条形码内容，支持占位符
        self.barcode_type = barcode_type  # 条形码类型
        self.selected = False
        self.canvas_ids = []  # 存储canvas对象
        self.element_type = "barcode"  # 元素类型
        self.barcode_image = None  # 缓存的条形码图片