"""图片处理模块"""

import os
from PIL import Image


class ImageHandler:
    """图片处理器"""
    
    @staticmethod
    def find_image_by_ksh(ksh_placeholder, data_row=None, excel_data=None):
        """根据ksh占位符查找图片"""
        if excel_data is None or excel_data.empty:
            return None

        # 如果没有指定行，使用第一行数据用于预览
        if data_row is None:
            data_row = excel_data.iloc[0]

        # 替换占位符获取实际ksh值
        ksh_value = ksh_placeholder
        for col, value in data_row.items():
            placeholder = f"{{{col}}}"
            if placeholder in ksh_value:
                ksh_value = ksh_value.replace(placeholder, str(value))

        # 如果还包含未替换的占位符，返回None
        if "{" in ksh_value and "}" in ksh_value:
            return None

        # 在KSZP文件夹中查找图片
        kszp_folder = "KSZP"
        if not os.path.exists(kszp_folder):
            return None

        # 支持的图片格式
        image_extensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp"]

        for ext in image_extensions:
            image_path = os.path.join(kszp_folder, f"{ksh_value}{ext}")
            if os.path.exists(image_path):
                try:
                    return Image.open(image_path)
                except Exception as e:
                    print(f"无法打开图片 {image_path}: {e}")
                    continue

        return None
    
    @staticmethod
    def get_image_dpi(image):
        """获取图片DPI信息"""
        try:
            # 获取DPI信息，PIL返回的是(x_dpi, y_dpi)
            dpi = image.info.get('dpi', (72, 72))
            if isinstance(dpi, (tuple, list)) and len(dpi) >= 2:
                return dpi[0], dpi[1]
            elif isinstance(dpi, (int, float)):
                return dpi, dpi
            else:
                return 72, 72  # 默认DPI
        except:
            return 72, 72  # 默认DPI
    
    @staticmethod
    def validate_dpi(dpi_x, dpi_y, min_dpi=50, max_dpi=600):
        """验证DPI值是否合理"""
        def is_valid_dpi(dpi):
            return isinstance(dpi, (int, float)) and min_dpi <= dpi <= max_dpi
        
        if not is_valid_dpi(dpi_x) or not is_valid_dpi(dpi_y):
            return 72, 72  # 返回默认DPI
        return dpi_x, dpi_y
    
    @staticmethod
    def calculate_display_size(image, target_dpi=300):
        """计算图片在指定DPI下的显示尺寸（毫米）"""
        dpi_x, dpi_y = ImageHandler.get_image_dpi(image)
        dpi_x, dpi_y = ImageHandler.validate_dpi(dpi_x, dpi_y)
        
        # 计算物理尺寸（英寸）
        width_inch = image.width / dpi_x
        height_inch = image.height / dpi_y
        
        # 转换为毫米
        width_mm = width_inch * 25.4
        height_mm = height_inch * 25.4
        
        return width_mm, height_mm