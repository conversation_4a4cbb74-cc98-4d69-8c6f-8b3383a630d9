"""Excel数据处理模块"""

import pandas as pd
from tkinter import filedialog, messagebox
import tkinter as tk


class ExcelHandler:
    """Excel数据处理器"""
    
    def __init__(self):
        self.excel_data = None
    
    def load_excel(self):
        """加载Excel文件"""
        file_path = filedialog.askopenfilename(title="选择Excel文件", filetypes=[("Excel文件", "*.xlsx *.xls")])

        if file_path:
            try:
                # 读取Excel文件
                self.excel_data = pd.read_excel(file_path)
                print(f"Excel文件加载成功，共{len(self.excel_data)}行数据")
                return True
            except Exception as e:
                messagebox.showerror("错误", f"加载Excel文件失败: {e}")
                return False
        return False
    
    def get_preview_text(self):
        """获取Excel数据预览文本"""
        if self.excel_data is not None and not self.excel_data.empty:
            # 显示第一行数据和列名
            preview_text = "可用列名:\n"
            for col in self.excel_data.columns:
                preview_text += f"  {{{col}}}\n"

            preview_text += "\n第一行数据:\n"
            first_row = self.excel_data.iloc[0]
            for col, value in first_row.items():
                preview_text += f"  {col}: {value}\n"
            
            return preview_text
        return ""
    
    def update_excel_preview(self, preview_widget):
        """更新Excel数据预览窗口"""
        preview_text = self.get_preview_text()
        if preview_text:
            preview_widget.config(state="normal")
            preview_widget.delete(1.0, tk.END)
            preview_widget.insert(1.0, preview_text)
            preview_widget.config(state="disabled")
    
    def replace_placeholders(self, text, data_row=None):
        """替换文本中的占位符"""
        if self.excel_data is None or self.excel_data.empty:
            return text

        # 如果没有指定行，使用第一行
        if data_row is None:
            data_row = self.excel_data.iloc[0]

        result = text
        for col in self.excel_data.columns:
            placeholder = f"{{{col}}}"
            if placeholder in result:
                value = str(data_row[col]) if pd.notna(data_row[col]) else ""
                result = result.replace(placeholder, value)

        return result
    
    def get_row_count(self):
        """获取数据行数"""
        if self.excel_data is not None:
            return len(self.excel_data)
        return 0
    
    def get_row(self, index):
        """获取指定行的数据"""
        if self.excel_data is not None and 0 <= index < len(self.excel_data):
            return self.excel_data.iloc[index]
        return None