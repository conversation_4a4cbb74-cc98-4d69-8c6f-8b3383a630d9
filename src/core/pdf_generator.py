"""PDF生成核心模块"""

import os
import io
import barcode
from barcode.writer import ImageWriter
from PIL import Image
from reportlab.pdfgen import canvas
from reportlab.lib.units import mm

from ..utils.constants import A4_WIDTH_MM, A4_HEIGHT_MM
from ..utils.font_utils import get_font_path
from .image_handler import ImageHandler


class PDFGenerator:
    """PDF生成器"""
    
    def __init__(self, font_registered=False):
        self.font_registered = font_registered
    
    def validate_barcode_text(self, text, barcode_type):
        """验证并修正条形码文本"""
        if barcode_type in ["code128", "code39"]:
            # Code128 和 Code39 支持数字、字母和部分特殊字符
            allowed_chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-. $/+%"
            if barcode_type == "code39":
                # Code39 额外支持这些字符
                allowed_chars += "*"
            
            filtered_text = "".join(c for c in text if c in allowed_chars)
            if not filtered_text:
                filtered_text = "123456"  # 默认值
            return filtered_text
            
        elif barcode_type in ["ean13", "ean8"]:
            # EAN只支持数字
            digits_only = "".join(c for c in text if c.isdigit())
            target_length = 13 if barcode_type == "ean13" else 8
            
            if len(digits_only) >= target_length:
                return digits_only[:target_length - 1]  # 最后一位是校验位
            else:
                # 不足位数时用0补齐
                return digits_only.ljust(target_length - 1, "0")
        
        return text
    
    def generate_pdf_page(self, buffer, data_row, background_image, text_boxes, image_boxes, barcode_boxes, excel_data):
        """生成单页PDF"""
        c = canvas.Canvas(buffer, pagesize=(A4_WIDTH_MM * mm, A4_HEIGHT_MM * mm))

        # 绘制背景图片（预览使用缩放版本以加速生成）
        if background_image:
            try:
                # 预览使用已缩放的图片，速度更快
                temp_bg_path = "temp_background_preview.jpg"
                # 为预览生成适中分辨率的背景图片
                bg_resized = background_image.resize(
                    (int(A4_WIDTH_MM * mm * 72 / 25.4), int(A4_HEIGHT_MM * mm * 72 / 25.4)),
                    Image.Resampling.LANCZOS,
                )
                bg_resized.save(temp_bg_path, "JPEG", quality=85)  # 适中的质量以平衡速度和效果
                c.drawImage(temp_bg_path, 0, 0, width=A4_WIDTH_MM * mm, height=A4_HEIGHT_MM * mm)
                os.remove(temp_bg_path)
            except Exception as e:
                print(f"预览背景图片添加失败: {e}")

        # 绘制图片框
        for ib in image_boxes:
            image_data = ImageHandler.find_image_by_ksh(ib.ksh_placeholder, data_row, excel_data)
            if image_data:
                try:
                    # 保存为临时文件
                    temp_img_path = f"temp_image_{id(ib)}.jpg"
                    # 转换为RGB模式以支持JPEG保存
                    if image_data.mode != "RGB":
                        image_data = image_data.convert("RGB")
                    image_data.save(temp_img_path, "JPEG")

                    # 转换坐标（PDF坐标系原点在左下角）
                    x_pdf = ib.x * mm
                    y_pdf = (A4_HEIGHT_MM - ib.y - ib.height) * mm
                    width_pdf = ib.width * mm
                    height_pdf = ib.height * mm

                    c.drawImage(temp_img_path, x_pdf, y_pdf, width=width_pdf, height=height_pdf)

                    # 删除临时文件
                    os.remove(temp_img_path)
                except Exception as e:
                    print(f"图片添加失败: {e}")

        # 绘制条形码框
        for bb in barcode_boxes:
            try:
                # 处理条形码内容，替换占位符
                barcode_text = bb.text
                for col, value in data_row.items():
                    placeholder = f"{{{col}}}"
                    barcode_text = barcode_text.replace(placeholder, str(value))

                # 验证并修正条形码文本
                barcode_text = self.validate_barcode_text(barcode_text, bb.barcode_type)

                # 生成条形码图片
                barcode_class = barcode.get_barcode_class(bb.barcode_type)
                writer = ImageWriter()
                writer.format = "PNG"
                code = barcode_class(barcode_text, writer=writer)

                # 保存为临时文件
                temp_barcode_path = f"temp_barcode_{id(bb)}.png"

                # 通过内存缓冲区生成并保存条形码图片
                buffer_barcode = io.BytesIO()
                options = {
                    "write_text": True,  # 显示文本
                    "font_size": 10,  # 设置较小的字体大小
                    "text_distance": 5.0,  # 文字与条形码的距离，增加距离避免重合
                }
                
                # 尝试设置字体路径
                font_path = get_font_path()
                if font_path:
                    options["font_path"] = font_path
                    
                try:
                    code.write(buffer_barcode, options)
                except Exception as e:
                    print(f"使用自定义字体失败，使用默认字体: {e}")
                    # 移除font_path选项，使用默认字体
                    if "font_path" in options:
                        del options["font_path"]
                    code.write(buffer_barcode, options)
                buffer_barcode.seek(0)
                barcode_img = Image.open(buffer_barcode)

                # 转换为RGBA模式以支持透明度
                if barcode_img.mode != "RGBA":
                    barcode_img = barcode_img.convert("RGBA")

                # 将白色背景设置为透明
                data = barcode_img.getdata()
                new_data = []
                for item in data:
                    # 如果是白色或接近白色的像素，设置为透明
                    if len(item) >= 3 and item[0] > 240 and item[1] > 240 and item[2] > 240:
                        new_data.append((255, 255, 255, 0))  # 透明
                    else:
                        new_data.append(item)  # 保持原样

                barcode_img.putdata(new_data)
                barcode_img.save(temp_barcode_path, "PNG")

                # 确保文件已创建
                if os.path.exists(temp_barcode_path):
                    # 转换坐标（PDF坐标系原点在左下角）
                    x_pdf = bb.x * mm
                    y_pdf = (A4_HEIGHT_MM - bb.y - bb.height) * mm
                    width_pdf = bb.width * mm
                    height_pdf = bb.height * mm

                    # 使用mask参数处理透明背景，[250,255,250,255,250,255]会将白色像素设为透明
                    c.drawImage(
                        temp_barcode_path,
                        x_pdf,
                        y_pdf,
                        width=width_pdf,
                        height=height_pdf,
                        mask=[250, 255, 250, 255, 250, 255],
                    )

                    # 删除临时文件
                    os.remove(temp_barcode_path)
                else:
                    print(f"条形码文件创建失败: {temp_barcode_path}")

            except Exception as e:
                print(f"条形码添加失败: {e}")
                import traceback
                traceback.print_exc()

        # 绘制文本框
        for tb in text_boxes:
            # 处理文本内容，替换占位符
            text = tb.text
            for col, value in data_row.items():
                placeholder = f"{{{col}}}"
                text = text.replace(placeholder, str(value))

            # 转换坐标（PDF坐标系原点在左下角）
            x_pdf = tb.x * mm
            y_pdf = (A4_HEIGHT_MM - tb.y - tb.height) * mm

            # 设置字体
            if self.font_registered:
                c.setFont("SimHei", tb.font_size)
            else:
                c.setFont("Helvetica", tb.font_size)

            # 绘制文本（支持多行和正确的空格处理）
            lines = text.split("\n")
            line_height_pt = tb.font_size * tb.line_height

            for i, line in enumerate(lines):
                # 处理空格：将半角空格替换为全角空格，保持与预览界面一致
                processed_line = line.replace(" ", "　")  # 替换为全角空格
                y_line = y_pdf + tb.height * mm - (i + 1) * line_height_pt
                c.drawString(x_pdf, y_line, processed_line)

        c.save()

    def generate_batch_pdf(self, file_path, background_image, text_boxes, image_boxes, barcode_boxes, excel_data):
        """生成批量PDF"""
        c = canvas.Canvas(file_path, pagesize=(A4_WIDTH_MM * mm, A4_HEIGHT_MM * mm))

        total_rows = len(excel_data)

        for index, (_, data_row) in enumerate(excel_data.iterrows()):
            print(f"正在生成第 {index + 1}/{total_rows} 页...")

            # 绘制背景图片（批量生成使用原始分辨率）
            if background_image:
                try:
                    # 批量生成使用高分辨率背景图片
                    temp_bg_path = f"temp_background_batch_{index}.jpg"
                    # 保存原始分辨率的背景图片
                    if background_image.mode != "RGB":
                        bg_converted = background_image.convert("RGB")
                    else:
                        bg_converted = background_image
                    bg_converted.save(temp_bg_path, "JPEG", quality=95)  # 高质量保存
                    c.drawImage(temp_bg_path, 0, 0, width=A4_WIDTH_MM * mm, height=A4_HEIGHT_MM * mm)
                    os.remove(temp_bg_path)
                except Exception as e:
                    print(f"背景图片添加失败: {e}")

            # 绘制图片框
            for ib in image_boxes:
                image_data = ImageHandler.find_image_by_ksh(ib.ksh_placeholder, data_row, excel_data)
                if image_data:
                    try:
                        # 保存为临时文件
                        temp_img_path = f"temp_image_{id(ib)}_{index}.jpg"
                        # 转换为RGB模式以支持JPEG保存
                        if image_data.mode != "RGB":
                            image_data = image_data.convert("RGB")
                        image_data.save(temp_img_path, "JPEG", quality=95)  # 高质量保存

                        # 转换坐标（PDF坐标系原点在左下角）
                        x_pdf = ib.x * mm
                        y_pdf = (A4_HEIGHT_MM - ib.y - ib.height) * mm
                        width_pdf = ib.width * mm
                        height_pdf = ib.height * mm

                        c.drawImage(temp_img_path, x_pdf, y_pdf, width=width_pdf, height=height_pdf)

                        # 删除临时文件
                        os.remove(temp_img_path)
                    except Exception as e:
                        print(f"图片添加失败: {e}")

            # 绘制条形码框（逻辑与单页相同）
            for bb in barcode_boxes:
                try:
                    # 处理条形码内容，替换占位符
                    barcode_text = bb.text
                    for col, value in data_row.items():
                        placeholder = f"{{{col}}}"
                        barcode_text = barcode_text.replace(placeholder, str(value))

                    # 验证并修正条形码文本
                    barcode_text = self.validate_barcode_text(barcode_text, bb.barcode_type)

                    # 生成条形码图片
                    barcode_class = barcode.get_barcode_class(bb.barcode_type)
                    writer = ImageWriter()
                    writer.format = "PNG"
                    code = barcode_class(barcode_text, writer=writer)

                    # 保存为临时文件
                    temp_barcode_path = f"temp_barcode_{id(bb)}_{index}.png"

                    # 通过内存缓冲区生成并保存条形码图片
                    buffer_barcode = io.BytesIO()
                    options = {
                        "write_text": True,
                        "font_size": 10,
                        "text_distance": 5.0,
                    }
                    
                    # 尝试设置字体路径
                    font_path = get_font_path()
                    if font_path:
                        options["font_path"] = font_path
                        
                    try:
                        code.write(buffer_barcode, options)
                    except Exception:
                        # 移除font_path选项，使用默认字体
                        if "font_path" in options:
                            del options["font_path"]
                        code.write(buffer_barcode, options)
                    
                    buffer_barcode.seek(0)
                    barcode_img = Image.open(buffer_barcode)

                    # 转换为RGBA模式以支持透明度
                    if barcode_img.mode != "RGBA":
                        barcode_img = barcode_img.convert("RGBA")

                    # 将白色背景设置为透明
                    data = barcode_img.getdata()
                    new_data = []
                    for item in data:
                        if len(item) >= 3 and item[0] > 240 and item[1] > 240 and item[2] > 240:
                            new_data.append((255, 255, 255, 0))
                        else:
                            new_data.append(item)

                    barcode_img.putdata(new_data)
                    barcode_img.save(temp_barcode_path, "PNG")

                    if os.path.exists(temp_barcode_path):
                        # 转换坐标
                        x_pdf = bb.x * mm
                        y_pdf = (A4_HEIGHT_MM - bb.y - bb.height) * mm
                        width_pdf = bb.width * mm
                        height_pdf = bb.height * mm

                        c.drawImage(
                            temp_barcode_path,
                            x_pdf,
                            y_pdf,
                            width=width_pdf,
                            height=height_pdf,
                            mask=[250, 255, 250, 255, 250, 255],
                        )

                        os.remove(temp_barcode_path)

                except Exception as e:
                    print(f"条形码添加失败: {e}")

            # 绘制文本框
            for tb in text_boxes:
                # 处理文本内容，替换占位符
                text = tb.text
                for col, value in data_row.items():
                    placeholder = f"{{{col}}}"
                    text = text.replace(placeholder, str(value))

                # 转换坐标
                x_pdf = tb.x * mm
                y_pdf = (A4_HEIGHT_MM - tb.y - tb.height) * mm

                # 设置字体
                if self.font_registered:
                    c.setFont("SimHei", tb.font_size)
                else:
                    c.setFont("Helvetica", tb.font_size)

                # 绘制文本
                lines = text.split("\n")
                line_height_pt = tb.font_size * tb.line_height

                for i, line in enumerate(lines):
                    processed_line = line.replace(" ", "　")
                    y_line = y_pdf + tb.height * mm - (i + 1) * line_height_pt
                    c.drawString(x_pdf, y_line, processed_line)

            # 新页面（除了最后一页）
            if index < total_rows - 1:
                c.showPage()

        c.save()
        print(f"批量PDF生成完成: {file_path}")