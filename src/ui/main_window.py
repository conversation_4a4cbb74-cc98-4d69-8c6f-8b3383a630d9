"""主窗口UI模块"""

import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
import customtkinter as ctk
import io
import os
import math

from ..models.elements import TextBox, ImageBox, BarcodeBox
from ..core.excel_handler import ExcelHandler
from ..core.image_handler import ImageHandler
from ..core.pdf_generator import PDFGenerator
from ..utils.constants import A4_WIDTH_MM, A4_HEIGHT_MM, CHINESE_FONT_SIZES, BARCODE_TYPES
from ..utils.font_utils import setup_fonts
from ..utils.tk_utils import check_imagetk_availability

# 检查ImageTk可用性
IMAGETK_AVAILABLE, ImageTk = check_imagetk_availability()


class PDFGeneratorApp:
    """PDF生成器主应用"""

    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("PDF批量生成器")
        self.root.geometry("1200x800")

        # 数据存储
        self.background_image = None
        self.background_image_path = None
        self.background_photo = None
        self.text_boxes = []
        self.image_boxes = []
        self.barcode_boxes = []
        self.selected_element = None
        self.drag_data = {"x": 0, "y": 0}

        # 初始化处理器
        self.excel_handler = ExcelHandler()
        self.font_registered = setup_fonts()
        self.pdf_generator = PDFGenerator(self.font_registered)

        # 计算画布尺寸和缩放比例
        self.setup_canvas_scaling()

        # 创建界面
        self.setup_ui()

    def setup_canvas_scaling(self):
        """设置画布缩放比例"""
        # 获取屏幕DPI
        self.root.update_idletasks()
        self.screen_dpi = self.root.winfo_fpixels("1i")

        # 计算A4画布的像素尺寸
        # 使用标准72 DPI作为基准
        base_dpi = 72
        self.canvas_width = int(A4_WIDTH_MM * base_dpi / 25.4)
        self.canvas_height = int(A4_HEIGHT_MM * base_dpi / 25.4)

        # 缩放比例：像素/毫米 (用于坐标转换)
        self.scale_x = self.canvas_width / A4_WIDTH_MM
        self.scale_y = self.canvas_height / A4_HEIGHT_MM

        # 字体缩放逻辑：将PDF磅值转换为画布像素
        # 1磅 = 1/72英寸 = 25.4/72毫米
        # 字体像素大小 = 磅值 * 屏幕DPI / 72
        self.font_scale = self.screen_dpi / 72
        print(f"画布尺寸: {self.canvas_width}x{self.canvas_height}px, 缩放比例: {self.scale_x:.2f}x{self.scale_y:.2f}")

    def setup_ui(self):
        """创建用户界面"""
        # 主容器
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # 顶部工具栏
        self.setup_toolbar(main_frame)

        # 中间内容区域
        content_frame = ctk.CTkFrame(main_frame)
        content_frame.pack(fill="both", expand=True, pady=(5, 0))

        # 左侧画布区域
        self.setup_canvas_area(content_frame)

        # 右侧控制面板
        self.setup_control_panel(content_frame)

    def setup_toolbar(self, parent):
        """设置工具栏"""
        toolbar = ctk.CTkFrame(parent)
        toolbar.pack(fill="x", padx=5, pady=5)

        # 文件操作按钮
        ctk.CTkButton(toolbar, text="加载背景图", command=self.load_background).pack(side="left", padx=5)
        ctk.CTkButton(toolbar, text="导入Excel", command=self.load_excel).pack(side="left", padx=5)

        # 元素添加按钮
        ctk.CTkButton(toolbar, text="添加文本框", command=self.add_textbox).pack(side="left", padx=5)
        ctk.CTkButton(toolbar, text="添加图片框", command=self.add_imagebox).pack(side="left", padx=5)
        ctk.CTkButton(toolbar, text="添加条形码", command=self.add_barcodebox).pack(side="left", padx=5)

        # 分隔符
        separator = ctk.CTkFrame(toolbar, width=2, height=30)
        separator.pack(side="left", padx=10)

        # 删除按钮
        ctk.CTkButton(toolbar, text="删除选中元素", command=self.delete_selected_element, 
                     fg_color="red").pack(side="left", padx=5)

        # 右侧操作按钮
        ctk.CTkButton(toolbar, text="预览PDF", command=self.preview_pdf).pack(side="right", padx=5)
        ctk.CTkButton(toolbar, text="导出PDF", command=self.export_pdf).pack(side="right", padx=5)

    def setup_canvas_area(self, parent):
        """设置画布区域"""
        # 左侧画布容器
        canvas_container = ctk.CTkFrame(parent)
        canvas_container.pack(side="left", fill="both", expand=True, padx=(0, 5))

        # 画布标题
        canvas_title = ctk.CTkLabel(canvas_container, text="设计画布", font=("Arial", 16, "bold"))
        canvas_title.pack(pady=5)

        # 创建滚动画布
        canvas_frame = tk.Frame(canvas_container._canvas)
        canvas_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # 创建画布和滚动条
        self.canvas = tk.Canvas(
            canvas_frame,
            width=self.canvas_width,
            height=self.canvas_height,
            bg="white",
            scrollregion=(0, 0, self.canvas_width, self.canvas_height)
        )

        # 滚动条
        h_scrollbar = tk.Scrollbar(canvas_frame, orient="horizontal", command=self.canvas.xview)
        v_scrollbar = tk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        self.canvas.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)

        # 布局
        self.canvas.grid(row=0, column=0, sticky="nsew")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")

        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)

        # 绑定事件
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        self.canvas.bind("<B1-Motion>", self.on_canvas_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_canvas_release)

        # 绘制A4网格
        self.draw_a4_grid()

    def setup_control_panel(self, parent):
        """设置右侧控制面板"""
        # 右侧控制面板
        control_panel = ctk.CTkFrame(parent, width=300)
        control_panel.pack(side="right", fill="y")
        control_panel.pack_propagate(False)

        # Excel数据预览
        self.setup_excel_preview(control_panel)

        # 属性面板
        self.setup_property_panel(control_panel)

    def setup_excel_preview(self, parent):
        """设置Excel数据预览"""
        # Excel数据预览
        excel_frame = ctk.CTkFrame(parent)
        excel_frame.pack(fill="x", padx=10, pady=10)

        excel_label = ctk.CTkLabel(excel_frame, text="Excel数据预览", font=("Arial", 14, "bold"))
        excel_label.pack(pady=5)

        # 创建Excel预览文本框
        self.excel_preview = scrolledtext.ScrolledText(
            excel_frame._canvas, height=8, state="disabled", wrap="word"
        )
        self.excel_preview.pack(fill="x", padx=5, pady=5)

    def setup_property_panel(self, parent):
        """设置属性面板"""
        # 属性面板
        property_frame = ctk.CTkFrame(parent)
        property_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))

        property_label = ctk.CTkLabel(property_frame, text="属性面板", font=("Arial", 14, "bold"))
        property_label.pack(pady=5)

        # 属性面板容器
        self.property_container = ctk.CTkFrame(property_frame)
        self.property_container.pack(fill="both", expand=True, padx=5, pady=5)

        # 默认显示提示信息
        self.default_label = ctk.CTkLabel(self.property_container, text="请选择一个元素来编辑属性")
        self.default_label.pack(expand=True)

    # 文件操作方法
    def load_excel(self):
        """加载Excel文件"""
        if self.excel_handler.load_excel():
            self.excel_handler.update_excel_preview(self.excel_preview)

    def load_background(self):
        """加载背景图片"""
        file_path = filedialog.askopenfilename(
            title="选择背景图片",
            filetypes=[("图片文件", "*.png *.jpg *.jpeg *.gif *.bmp")]
        )

        if file_path:
            try:
                from PIL import Image
                self.background_image = Image.open(file_path)
                self.background_image_path = file_path
                
                # 重新计算画布缩放
                self.update_canvas_for_background()
                
                print(f"背景图片加载成功: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"加载背景图片失败: {e}")

    def update_canvas_for_background(self):
        """根据背景图片更新画布"""
        if not self.background_image:
            return

        # 获取图片DPI和尺寸
        width_mm, height_mm = ImageHandler.calculate_display_size(self.background_image)
        
        # 更新画布尺寸
        self.canvas_width = int(width_mm * self.scale_x)
        self.canvas_height = int(height_mm * self.scale_y)
        
        # 重新配置画布
        self.canvas.configure(
            width=self.canvas_width,
            height=self.canvas_height,
            scrollregion=(0, 0, self.canvas_width, self.canvas_height)
        )

        # 重新绘制背景
        self.draw_background()
        
        # 重新绘制所有元素
        self.redraw_all_elements()

    def draw_background(self):
        """绘制背景图片"""
        if not self.background_image:
            return

        try:
            # 缩放背景图片以适应画布
            bg_resized = self.background_image.resize(
                (self.canvas_width, self.canvas_height), 
                Image.Resampling.LANCZOS
            )
            
            if IMAGETK_AVAILABLE:
                self.background_photo = ImageTk.PhotoImage(bg_resized)
                self.canvas.create_image(0, 0, anchor="nw", image=self.background_photo, tags="background")
            else:
                # 使用替代方案
                self.canvas.create_rectangle(0, 0, self.canvas_width, self.canvas_height, 
                                           fill="lightgray", outline="gray", tags="background")
                self.canvas.create_text(self.canvas_width//2, self.canvas_height//2, 
                                      text="背景图片预览不可用", tags="background")
        except Exception as e:
            print(f"背景图片显示失败: {e}")

    def draw_a4_grid(self):
        """绘制A4网格"""
        # 清除旧的网格
        self.canvas.delete("grid")
        
        # 绘制边框
        self.canvas.create_rectangle(0, 0, self.canvas_width, self.canvas_height, 
                                   outline="black", width=2, tags="grid")
        
        # 绘制网格线 (每10mm一条)
        grid_spacing_mm = 10
        grid_spacing_px = grid_spacing_mm * self.scale_x
        
        # 垂直线
        x = grid_spacing_px
        while x < self.canvas_width:
            self.canvas.create_line(x, 0, x, self.canvas_height, 
                                  fill="lightgray", width=1, tags="grid")
            x += grid_spacing_px
        
        # 水平线
        y = grid_spacing_px
        while y < self.canvas_height:
            self.canvas.create_line(0, y, self.canvas_width, y, 
                                  fill="lightgray", width=1, tags="grid")
            y += grid_spacing_px

    # 元素操作方法
    def add_textbox(self):
        """添加文本框"""
        x_mm = A4_WIDTH_MM / 2 - 50
        y_mm = A4_HEIGHT_MM / 2 - 15
        
        textbox = TextBox(x_mm, y_mm, text="文本框")
        self.text_boxes.append(textbox)
        self.draw_textbox(textbox)
        self.select_element(textbox)

    def add_imagebox(self):
        """添加图片框"""
        x_mm = A4_WIDTH_MM / 2 - 15
        y_mm = A4_HEIGHT_MM / 2 - 20
        
        imagebox = ImageBox(x_mm, y_mm)
        self.image_boxes.append(imagebox)
        self.draw_imagebox(imagebox)
        self.select_element(imagebox)

    def add_barcodebox(self):
        """添加条形码框"""
        x_mm = A4_WIDTH_MM / 2 - 30
        y_mm = A4_HEIGHT_MM / 2 - 8
        
        barcodebox = BarcodeBox(x_mm, y_mm)
        self.barcode_boxes.append(barcodebox)
        self.draw_barcodebox(barcodebox)
        self.select_element(barcodebox)

    def delete_selected_element(self):
        """删除选中的元素"""
        if not self.selected_element:
            messagebox.showwarning("警告", "请先选择要删除的元素")
            return

        element = self.selected_element
        
        # 从对应列表中移除
        if element.element_type == "text":
            self.text_boxes.remove(element)
        elif element.element_type == "image":
            self.image_boxes.remove(element)
        elif element.element_type == "barcode":
            self.barcode_boxes.remove(element)

        # 从画布中删除
        for canvas_id in element.canvas_ids:
            self.canvas.delete(canvas_id)

        # 清空选择
        self.selected_element = None
        self.clear_property_panel()

    # 绘制方法的简化版本
    def draw_textbox(self, textbox):
        """绘制文本框"""
        # 清除旧的绘制内容
        for canvas_id in textbox.canvas_ids:
            self.canvas.delete(canvas_id)
        textbox.canvas_ids.clear()

        # 转换坐标
        x_px = textbox.x * self.scale_x
        y_px = textbox.y * self.scale_y
        width_px = textbox.width * self.scale_x
        height_px = textbox.height * self.scale_y

        # 绘制边框
        border_color = "red" if textbox.selected else "blue"
        border_width = 2 if textbox.selected else 1
        
        rect_id = self.canvas.create_rectangle(
            x_px, y_px, x_px + width_px, y_px + height_px,
            outline=border_color, width=border_width, fill="", tags=f"textbox_{id(textbox)}"
        )
        textbox.canvas_ids.append(rect_id)

        # 绘制文本
        display_text = self.excel_handler.replace_placeholders(textbox.text)
        font_size_px = int(textbox.font_size * self.font_scale)
        
        text_id = self.canvas.create_text(
            x_px + 5, y_px + 5, text=display_text, anchor="nw",
            font=("Arial", font_size_px), fill="black", width=width_px - 10,
            tags=f"textbox_{id(textbox)}"
        )
        textbox.canvas_ids.append(text_id)

    def draw_imagebox(self, imagebox):
        """绘制图片框"""
        # 清除旧的绘制内容
        for canvas_id in imagebox.canvas_ids:
            self.canvas.delete(canvas_id)
        imagebox.canvas_ids.clear()

        # 转换坐标
        x_px = imagebox.x * self.scale_x
        y_px = imagebox.y * self.scale_y
        width_px = imagebox.width * self.scale_x
        height_px = imagebox.height * self.scale_y

        # 绘制边框
        border_color = "red" if imagebox.selected else "green"
        border_width = 2 if imagebox.selected else 1
        
        rect_id = self.canvas.create_rectangle(
            x_px, y_px, x_px + width_px, y_px + height_px,
            outline=border_color, width=border_width, fill="lightblue",
            tags=f"imagebox_{id(imagebox)}"
        )
        imagebox.canvas_ids.append(rect_id)

        # 尝试显示实际图片
        image_data = ImageHandler.find_image_by_ksh(
            imagebox.ksh_placeholder, 
            excel_data=self.excel_handler.excel_data
        )
        
        if image_data and IMAGETK_AVAILABLE:
            try:
                # 缩放图片以适应框架
                img_resized = image_data.resize((int(width_px), int(height_px)), Image.Resampling.LANCZOS)
                imagebox.image_data = ImageTk.PhotoImage(img_resized)
                
                img_id = self.canvas.create_image(
                    x_px + width_px/2, y_px + height_px/2,
                    image=imagebox.image_data, tags=f"imagebox_{id(imagebox)}"
                )
                imagebox.canvas_ids.append(img_id)
            except Exception as e:
                print(f"图片显示失败: {e}")
        else:
            # 显示占位符文本
            text_id = self.canvas.create_text(
                x_px + width_px/2, y_px + height_px/2,
                text=imagebox.ksh_placeholder, font=("Arial", 10),
                tags=f"imagebox_{id(imagebox)}"
            )
            imagebox.canvas_ids.append(text_id)

    def draw_barcodebox(self, barcodebox):
        """绘制条形码框"""
        # 清除旧的绘制内容
        for canvas_id in barcodebox.canvas_ids:
            self.canvas.delete(canvas_id)
        barcodebox.canvas_ids.clear()

        # 转换坐标
        x_px = barcodebox.x * self.scale_x
        y_px = barcodebox.y * self.scale_y
        width_px = barcodebox.width * self.scale_x
        height_px = barcodebox.height * self.scale_y

        # 绘制边框
        border_color = "red" if barcodebox.selected else "purple"
        border_width = 2 if barcodebox.selected else 1
        
        rect_id = self.canvas.create_rectangle(
            x_px, y_px, x_px + width_px, y_px + height_px,
            outline=border_color, width=border_width, fill="lightyellow",
            tags=f"barcodebox_{id(barcodebox)}"
        )
        barcodebox.canvas_ids.append(rect_id)

        # 显示条形码文本
        display_text = self.excel_handler.replace_placeholders(barcodebox.text)
        text_id = self.canvas.create_text(
            x_px + width_px/2, y_px + height_px/2,
            text=f"{barcodebox.barcode_type.upper()}\n{display_text}",
            font=("Arial", 8), justify="center",
            tags=f"barcodebox_{id(barcodebox)}"
        )
        barcodebox.canvas_ids.append(text_id)

    def redraw_all_elements(self):
        """重新绘制所有元素"""
        for textbox in self.text_boxes:
            self.draw_textbox(textbox)
        for imagebox in self.image_boxes:
            self.draw_imagebox(imagebox)
        for barcodebox in self.barcode_boxes:
            self.draw_barcodebox(barcodebox)

    # 事件处理方法
    def on_canvas_click(self, event):
        """画布点击事件"""
        # 获取点击位置的所有项目
        clicked_items = self.canvas.find_overlapping(event.x-1, event.y-1, event.x+1, event.y+1)
        
        # 查找被点击的元素
        for item in clicked_items:
            tags = self.canvas.gettags(item)
            for tag in tags:
                if tag.startswith("textbox_"):
                    element_id = tag.split("_")[1]
                    for textbox in self.text_boxes:
                        if str(id(textbox)) == element_id:
                            self.select_element(textbox)
                            self.drag_data["x"] = event.x
                            self.drag_data["y"] = event.y
                            return
                elif tag.startswith("imagebox_"):
                    element_id = tag.split("_")[1]
                    for imagebox in self.image_boxes:
                        if str(id(imagebox)) == element_id:
                            self.select_element(imagebox)
                            self.drag_data["x"] = event.x
                            self.drag_data["y"] = event.y
                            return
                elif tag.startswith("barcodebox_"):
                    element_id = tag.split("_")[1]
                    for barcodebox in self.barcode_boxes:
                        if str(id(barcodebox)) == element_id:
                            self.select_element(barcodebox)
                            self.drag_data["x"] = event.x
                            self.drag_data["y"] = event.y
                            return
        
        # 如果没有点击到元素，取消选择
        self.select_element(None)

    def on_canvas_drag(self, event):
        """画布拖拽事件"""
        if not self.selected_element:
            return

        # 计算拖拽距离
        dx_px = event.x - self.drag_data["x"]
        dy_px = event.y - self.drag_data["y"]

        # 转换为毫米
        dx_mm = dx_px / self.scale_x
        dy_mm = dy_px / self.scale_y

        # 更新元素位置
        self.selected_element.x += dx_mm
        self.selected_element.y += dy_mm

        # 重新绘制元素
        if self.selected_element.element_type == "text":
            self.draw_textbox(self.selected_element)
        elif self.selected_element.element_type == "image":
            self.draw_imagebox(self.selected_element)
        elif self.selected_element.element_type == "barcode":
            self.draw_barcodebox(self.selected_element)

        # 更新拖拽起始点
        self.drag_data["x"] = event.x
        self.drag_data["y"] = event.y

    def on_canvas_release(self, event):
        """画布释放事件"""
        pass

    def select_element(self, element):
        """选择元素"""
        # 取消之前的选择
        if self.selected_element:
            self.selected_element.selected = False
            if self.selected_element.element_type == "text":
                self.draw_textbox(self.selected_element)
            elif self.selected_element.element_type == "image":
                self.draw_imagebox(self.selected_element)
            elif self.selected_element.element_type == "barcode":
                self.draw_barcodebox(self.selected_element)

        # 选择新元素
        self.selected_element = element
        if element:
            element.selected = True
            if element.element_type == "text":
                self.draw_textbox(element)
            elif element.element_type == "image":
                self.draw_imagebox(element)
            elif element.element_type == "barcode":
                self.draw_barcodebox(element)
            self.update_property_panel()
        else:
            self.clear_property_panel()

    def update_property_panel(self):
        """更新属性面板"""
        # 清除现有内容
        for widget in self.property_container.winfo_children():
            widget.destroy()

        if not self.selected_element:
            self.default_label = ctk.CTkLabel(self.property_container, text="请选择一个元素来编辑属性")
            self.default_label.pack(expand=True)
            return

        element = self.selected_element

        if element.element_type == "text":
            self.setup_textbox_properties(element)
        elif element.element_type == "image":
            self.setup_imagebox_properties(element)
        elif element.element_type == "barcode":
            self.setup_barcodebox_properties(element)

    def setup_textbox_properties(self, textbox):
        """设置文本框属性面板"""
        # 文本内容
        ctk.CTkLabel(self.property_container, text="文本内容:").pack(anchor="w", padx=5, pady=2)
        self.text_entry = ctk.CTkTextbox(self.property_container, height=60)
        self.text_entry.insert("1.0", textbox.text)
        self.text_entry.bind("<KeyRelease>", self.on_text_change)
        self.text_entry.pack(fill="x", padx=5, pady=2)

        # 字体大小
        ctk.CTkLabel(self.property_container, text="字体大小:").pack(anchor="w", padx=5, pady=2)
        self.fontsize_var = ctk.StringVar(value=textbox.font_size_name)
        fontsize_menu = ctk.CTkOptionMenu(
            self.property_container, 
            values=list(CHINESE_FONT_SIZES.keys()),
            variable=self.fontsize_var,
            command=self.on_fontsize_change
        )
        fontsize_menu.pack(fill="x", padx=5, pady=2)

    def setup_imagebox_properties(self, imagebox):
        """设置图片框属性面板"""
        ctk.CTkLabel(self.property_container, text="KSH占位符:").pack(anchor="w", padx=5, pady=2)
        self.ksh_entry = ctk.CTkEntry(self.property_container)
        self.ksh_entry.insert(0, imagebox.ksh_placeholder)
        self.ksh_entry.bind("<KeyRelease>", self.on_ksh_change)
        self.ksh_entry.pack(fill="x", padx=5, pady=2)

    def setup_barcodebox_properties(self, barcodebox):
        """设置条形码框属性面板"""
        # 条形码内容
        ctk.CTkLabel(self.property_container, text="条形码内容:").pack(anchor="w", padx=5, pady=2)
        self.barcode_text_entry = ctk.CTkEntry(self.property_container)
        self.barcode_text_entry.insert(0, barcodebox.text)
        self.barcode_text_entry.bind("<KeyRelease>", self.on_barcode_text_change)
        self.barcode_text_entry.pack(fill="x", padx=5, pady=2)

        # 条形码类型
        ctk.CTkLabel(self.property_container, text="条形码类型:").pack(anchor="w", padx=5, pady=2)
        self.barcode_type_var = ctk.StringVar(value=barcodebox.barcode_type)
        barcode_type_menu = ctk.CTkOptionMenu(
            self.property_container,
            values=BARCODE_TYPES,
            variable=self.barcode_type_var,
            command=self.on_barcode_type_change
        )
        barcode_type_menu.pack(fill="x", padx=5, pady=2)

    def clear_property_panel(self):
        """清空属性面板"""
        for widget in self.property_container.winfo_children():
            widget.destroy()
        self.default_label = ctk.CTkLabel(self.property_container, text="请选择一个元素来编辑属性")
        self.default_label.pack(expand=True)

    # 属性变更事件
    def on_text_change(self, event=None):
        """文本内容变更"""
        if self.selected_element and self.selected_element.element_type == "text":
            self.selected_element.text = self.text_entry.get("1.0", "end-1c")
            self.draw_textbox(self.selected_element)

    def on_fontsize_change(self, font_size_name=None):
        """字体大小变更"""
        if self.selected_element and self.selected_element.element_type == "text":
            font_size_name = font_size_name or self.fontsize_var.get()
            self.selected_element.font_size_name = font_size_name
            self.selected_element.font_size = CHINESE_FONT_SIZES.get(font_size_name, 12)
            self.draw_textbox(self.selected_element)

    def on_ksh_change(self, event=None):
        """KSH占位符变更"""
        if self.selected_element and self.selected_element.element_type == "image":
            self.selected_element.ksh_placeholder = self.ksh_entry.get()
            self.draw_imagebox(self.selected_element)

    def on_barcode_text_change(self, event=None):
        """条形码内容变更"""
        if self.selected_element and self.selected_element.element_type == "barcode":
            self.selected_element.text = self.barcode_text_entry.get()
            self.draw_barcodebox(self.selected_element)

    def on_barcode_type_change(self, barcode_type=None):
        """条形码类型变更"""
        if self.selected_element and self.selected_element.element_type == "barcode":
            barcode_type = barcode_type or self.barcode_type_var.get()
            self.selected_element.barcode_type = barcode_type
            self.draw_barcodebox(self.selected_element)

    # PDF操作方法
    def preview_pdf(self):
        """预览PDF"""
        if not self.excel_handler.excel_data or self.excel_handler.excel_data.empty:
            messagebox.showwarning("警告", "请先导入Excel数据")
            return

        try:
            buffer = io.BytesIO()
            data_row = self.excel_handler.get_row(0)
            
            self.pdf_generator.generate_pdf_page(
                buffer, data_row, self.background_image,
                self.text_boxes, self.image_boxes, self.barcode_boxes,
                self.excel_handler.excel_data
            )

            # 保存预览文件
            preview_path = "preview.pdf"
            with open(preview_path, "wb") as f:
                f.write(buffer.getvalue())

            messagebox.showinfo("成功", f"预览PDF已生成: {preview_path}")

        except Exception as e:
            messagebox.showerror("错误", f"预览生成失败: {e}")

    def export_pdf(self):
        """导出批量PDF"""
        if not self.excel_handler.excel_data or self.excel_handler.excel_data.empty:
            messagebox.showwarning("警告", "请先导入Excel数据")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存PDF文件",
            defaultextension=".pdf",
            filetypes=[("PDF文件", "*.pdf")]
        )

        if file_path:
            try:
                self.pdf_generator.generate_batch_pdf(
                    file_path, self.background_image,
                    self.text_boxes, self.image_boxes, self.barcode_boxes,
                    self.excel_handler.excel_data
                )
                messagebox.showinfo("成功", f"批量PDF已生成: {file_path}")

            except Exception as e:
                messagebox.showerror("错误", f"批量生成失败: {e}")

    def run(self):
        """运行应用"""
        self.root.mainloop()