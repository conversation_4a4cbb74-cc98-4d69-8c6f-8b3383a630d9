"""PDF批量生成器 - 完全重构版本

功能：
1. 显示A4横向画布（297×210mm），根据屏幕DPI等比缩放
2. 加载背景图片，等比例铺满整个画布
3. 导入Excel数据，支持占位符系统
4. 添加文本框，支持拖拽、编辑、占位符
5. 预览PDF（第一条数据）
6. 批量导出PDF（所有数据）
"""

import os
import sys
import glob
from pathlib import Path


# 修复Tcl/Tk环境变量问题
def fix_tcl_tk_environment():
    """修复uv虚拟环境中的Tcl/Tk配置问题"""
    try:
        # 查找Tcl库
        tcl_init_files = glob.glob(os.path.join(sys.base_prefix, "lib", "tcl*", "init.tcl"))
        if tcl_init_files:
            os.environ["TCL_LIBRARY"] = str(Path(tcl_init_files[0]).parent)

        # 查找Tk库
        tk_files = glob.glob(os.path.join(sys.base_prefix, "lib", "tk*"))
        if tk_files:
            os.environ["TK_LIBRARY"] = tk_files[0]

        # 如果上面的方法失败，尝试更通用的路径
        if "TCL_LIBRARY" not in os.environ:
            # 对于uv管理的Python环境
            python_lib_path = os.path.join(sys.base_prefix, "lib")
            if os.path.exists(python_lib_path):
                tcl_dirs = glob.glob(os.path.join(python_lib_path, "tcl*"))
                tk_dirs = glob.glob(os.path.join(python_lib_path, "tk*"))

                if tcl_dirs:
                    os.environ["TCL_LIBRARY"] = tcl_dirs[0]
                if tk_dirs:
                    os.environ["TK_LIBRARY"] = tk_dirs[0]
    except Exception as e:
        print(f"警告: 无法自动配置Tcl/Tk环境: {e}")


# 在导入tkinter之前修复环境
fix_tcl_tk_environment()

import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
import customtkinter as ctk
import pandas as pd
from PIL import Image
import base64
import io

# 尝试导入ImageTk，如果失败则使用替代方案
try:
    from PIL import ImageTk

    IMAGETK_AVAILABLE = True
except (ImportError, TypeError) as e:
    print(f"警告: ImageTk不可用 ({e})，将使用替代显示方案")
    IMAGETK_AVAILABLE = False
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import io
import math
from datetime import datetime
import barcode
from barcode.writer import ImageWriter

# 设置CustomTkinter外观
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

# 中文字号对应磅值表
CHINESE_FONT_SIZES = {
    "初号": 42,
    "小初": 36,
    "一号": 26,
    "小一": 24,
    "二号": 22,
    "小二": 18,
    "三号": 16,
    "小三": 15,
    "四号": 14,
    "小四": 12,
    "五号": 10.5,
    "小五": 9,
    "六号": 7.5,
    "小六": 6.5,
}

# 反向映射：磅值到中文字号
POINT_TO_CHINESE = {v: k for k, v in CHINESE_FONT_SIZES.items()}


class TextBox:
    """文本框类"""

    def __init__(self, x, y, width=100, height=30, text="文本框", font_size_name="小四", line_height=1.2):
        self.x = x  # 毫米
        self.y = y  # 毫米
        self.width = width  # 毫米
        self.height = height  # 毫米
        self.text = text
        self.font_size_name = font_size_name  # 中文字号名称
        self.font_size = CHINESE_FONT_SIZES.get(font_size_name, 12)  # 对应的磅值
        self.line_height = line_height
        self.selected = False
        self.canvas_ids = []  # 改为列表存储多个canvas对象
        self.element_type = "text"  # 元素类型


class ImageBox:
    """图片框类"""

    def __init__(self, x, y, width=30, height=40, ksh_placeholder="{ksh}"):
        self.x = x  # 毫米
        self.y = y  # 毫米
        self.width = width  # 毫米
        self.height = height  # 毫米
        self.ksh_placeholder = ksh_placeholder  # ksh占位符
        self.selected = False
        self.canvas_ids = []  # 存储canvas对象
        self.element_type = "image"  # 元素类型
        self.image_data = None  # 缓存的图片数据


class BarcodeBox:
    """条形码框类"""

    def __init__(self, x, y, width=60, height=16, text="{ksh}", barcode_type="code128"):
        self.x = x  # 毫米
        self.y = y  # 毫米
        self.width = width  # 毫米（15:4比例，默认60mm宽）
        self.height = height  # 毫米（15:4比例，默认16mm高）
        self.text = text  # 条形码内容，支持占位符
        self.barcode_type = barcode_type  # 条形码类型
        self.selected = False
        self.canvas_ids = []  # 存储canvas对象
        self.element_type = "barcode"  # 元素类型
        self.barcode_image = None  # 缓存的条形码图片


class PDFGenerator:
    """PDF生成器主窗口"""

    # A4纸张尺寸（横向，毫米）
    A4_WIDTH_MM = 297
    A4_HEIGHT_MM = 210

    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("PDF批量生成器")
        self.root.geometry("1200x800")

        # 数据存储
        self.excel_data = None
        self.background_image = None
        self.background_image_path = None  # 保存原始背景图片路径
        self.background_photo = None
        self.text_boxes = []
        self.image_boxes = []  # 新增图片框列表
        self.barcode_boxes = []  # 新增条形码框列表
        self.selected_element = None  # 改为通用的选中元素
        self.drag_data = {"x": 0, "y": 0}

        # 计算画布尺寸和缩放比例
        self.setup_canvas_scaling()

        # 创建界面
        self.setup_ui()

        # 注册字体
        self.setup_fonts()

    def setup_canvas_scaling(self):
        """设置画布缩放比例"""
        # 获取屏幕DPI
        self.root.update_idletasks()
        self.screen_dpi = self.root.winfo_fpixels("1i")

        # 计算A4画布的像素尺寸
        # 使用标准72 DPI作为基准
        base_dpi = 72
        self.canvas_width = int(self.A4_WIDTH_MM * base_dpi / 25.4)
        self.canvas_height = int(self.A4_HEIGHT_MM * base_dpi / 25.4)

        # 缩放比例：像素/毫米 (用于坐标转换)
        self.scale_x = self.canvas_width / self.A4_WIDTH_MM
        self.scale_y = self.canvas_height / self.A4_HEIGHT_MM

        # 字体缩放逻辑：将PDF磅值转换为画布像素
        # 1磅 = 1/72英寸 = 25.4/72毫米
        # 画布字体像素 = 磅值 * (25.4/72) * scale_x * 屏幕DPI修正
        # Tkinter字体以磅为单位，但需要考虑屏幕DPI的影响
        base_conversion = (25.4 / 72.0) * self.scale_x
        # 增加屏幕DPI修正系数，确保显示尺寸与PDF一致
        dpi_correction = self.screen_dpi / 72.0
        self.font_pt_to_px = base_conversion * dpi_correction

        print(f"屏幕DPI: {self.screen_dpi:.1f}")
        print(f"画布尺寸: {self.canvas_width}x{self.canvas_height} 像素")
        print(f"坐标缩放比例: {self.scale_x:.2f} 像素/毫米")
        print(f"字体转换比例: {self.font_pt_to_px:.2f} 像素/磅")

    def setup_fonts(self):
        """设置字体"""
        # 尝试注册黑体字体
        font_paths = [
            "/System/Library/Fonts/STHeiti Light.ttc",  # macOS
            "/System/Library/Fonts/Helvetica.ttc",  # macOS备选
            "C:/Windows/Fonts/simhei.ttf",  # Windows
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
        ]

        self.font_registered = False
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    pdfmetrics.registerFont(TTFont("SimHei", font_path))
                    self.font_registered = True
                    print(f"已注册字体: {font_path}")
                    break
                except:
                    continue

        if not self.font_registered:
            print("警告: 未找到合适的中文字体，将使用默认字体")
    
    def get_font_path(self):
        """获取黑体字体路径，用于条形码文字"""
        font_paths = [
            "/System/Library/Fonts/STHeiti Light.ttc",  # macOS黑体
            "/System/Library/Fonts/PingFang.ttc",      # macOS苹方字体
            "C:/Windows/Fonts/simhei.ttf",              # Windows黑体
            "C:/Windows/Fonts/msyh.ttc",                # Windows微软雅黑
            "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",  # Linux粗体
            "/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf",  # Linux Liberation粗体
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                return font_path
        
        # 如果没有找到合适的字体，返回None使用默认字体
        return None

    def setup_ui(self):
        """创建用户界面"""
        # 工具栏
        toolbar = ctk.CTkFrame(self.root)
        toolbar.pack(fill="x", padx=5, pady=5)

        # 工具栏按钮
        ctk.CTkButton(toolbar, text="加载背景图", command=self.load_background).pack(side="left", padx=5)
        ctk.CTkButton(toolbar, text="加载Excel", command=self.load_excel).pack(side="left", padx=5)
        ctk.CTkButton(toolbar, text="添加文本框", command=self.add_textbox).pack(side="left", padx=5)
        ctk.CTkButton(toolbar, text="添加图片", command=self.add_imagebox).pack(side="left", padx=5)
        ctk.CTkButton(toolbar, text="添加条形码", command=self.add_barcodebox).pack(side="left", padx=5)
        ctk.CTkButton(toolbar, text="预览PDF", command=self.preview_pdf).pack(side="left", padx=5)
        ctk.CTkButton(toolbar, text="导出PDF", command=self.export_pdf).pack(side="left", padx=5)

        # 主工作区
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # 左侧画布区域
        canvas_frame = ctk.CTkFrame(main_frame)
        canvas_frame.pack(side="left", fill="both", expand=True, padx=(0, 5))

        # 创建带滚动条的画布
        self.canvas = tk.Canvas(
            canvas_frame,
            width=self.canvas_width,
            height=self.canvas_height,
            bg="white",
            scrollregion=(0, 0, self.canvas_width, self.canvas_height),
        )

        # 滚动条
        v_scrollbar = tk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        h_scrollbar = tk.Scrollbar(canvas_frame, orient="horizontal", command=self.canvas.xview)
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 布局画布和滚动条
        self.canvas.pack(side="left", fill="both", expand=True)
        v_scrollbar.pack(side="right", fill="y")
        h_scrollbar.pack(side="bottom", fill="x")

        # 绑定画布事件
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        self.canvas.bind("<B1-Motion>", self.on_canvas_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_canvas_release)

        # 右侧属性面板
        self.property_frame = ctk.CTkFrame(main_frame, width=300)
        self.property_frame.pack(side="right", fill="y")
        self.property_frame.pack_propagate(False)

        self.setup_property_panel()

        # 绘制A4网格
        self.draw_a4_grid()

    def setup_property_panel(self):
        """设置属性面板"""
        ctk.CTkLabel(self.property_frame, text="属性面板", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 文本内容
        ctk.CTkLabel(self.property_frame, text="文本内容:").pack(anchor="w", padx=10)
        self.text_entry = scrolledtext.ScrolledText(self.property_frame, height=4, width=30)
        self.text_entry.pack(padx=10, pady=5, fill="x")
        self.text_entry.bind("<KeyRelease>", self.on_text_change)

        # 字号设置
        ctk.CTkLabel(self.property_frame, text="字号:").pack(anchor="w", padx=10, pady=(10, 0))
        self.fontsize_var = tk.StringVar(value="小四")
        font_size_options = list(CHINESE_FONT_SIZES.keys())
        self.fontsize_combo = ctk.CTkComboBox(
            self.property_frame, values=font_size_options, variable=self.fontsize_var, command=self.on_fontsize_change
        )
        self.fontsize_combo.pack(padx=10, pady=5, fill="x")

        # 行高设置
        ctk.CTkLabel(self.property_frame, text="行高:").pack(anchor="w", padx=10, pady=(10, 0))
        self.lineheight_var = tk.StringVar(value="1.2")
        self.lineheight_entry = ctk.CTkEntry(self.property_frame, textvariable=self.lineheight_var)
        self.lineheight_entry.pack(padx=10, pady=5, fill="x")
        self.lineheight_entry.bind("<KeyRelease>", self.on_lineheight_change)
        # 绑定焦点离开事件，确保输入完成后立即更新
        self.lineheight_entry.bind("<FocusOut>", self.on_lineheight_change)

        # 条形码类型设置
        ctk.CTkLabel(self.property_frame, text="条形码类型:").pack(anchor="w", padx=10, pady=(10, 0))
        self.barcode_type_var = tk.StringVar(value="code128")
        barcode_type_options = ["code128", "code39", "ean13", "ean8"]
        self.barcode_type_combo = ctk.CTkComboBox(
            self.property_frame,
            values=barcode_type_options,
            variable=self.barcode_type_var,
            command=self.on_barcode_type_change,
        )
        self.barcode_type_combo.pack(padx=10, pady=5, fill="x")

        # 条形码尺寸设置
        size_frame = ctk.CTkFrame(self.property_frame)
        size_frame.pack(padx=10, pady=(10, 5), fill="x")

        ctk.CTkLabel(size_frame, text="宽度(mm):").pack(side="left", padx=(5, 2))
        self.barcode_width_var = tk.StringVar(value="60")
        self.barcode_width_entry = ctk.CTkEntry(size_frame, textvariable=self.barcode_width_var, width=60)
        self.barcode_width_entry.pack(side="left", padx=2)
        self.barcode_width_entry.bind("<KeyRelease>", self.on_barcode_width_change)
        self.barcode_width_entry.bind("<FocusOut>", self.on_barcode_width_change)

        ctk.CTkLabel(size_frame, text="高度(mm):").pack(side="left", padx=(10, 2))
        self.barcode_height_var = tk.StringVar(value="16")
        self.barcode_height_entry = ctk.CTkEntry(size_frame, textvariable=self.barcode_height_var, width=60)
        self.barcode_height_entry.pack(side="left", padx=2)
        self.barcode_height_entry.bind("<KeyRelease>", self.on_barcode_height_change)
        self.barcode_height_entry.bind("<FocusOut>", self.on_barcode_height_change)

        # 15:4比例重置按钮
        reset_ratio_button = ctk.CTkButton(
            self.property_frame,
            text="重置为15:4比例",
            command=self.reset_barcode_ratio,
            height=24,
            font=ctk.CTkFont(size=12),
        )
        reset_ratio_button.pack(padx=10, pady=5, fill="x")

        # 位置信息
        ctk.CTkLabel(self.property_frame, text="位置 (毫米):").pack(anchor="w", padx=10, pady=(10, 0))
        self.position_label = ctk.CTkLabel(self.property_frame, text="X: 0, Y: 0")
        self.position_label.pack(anchor="w", padx=10)

        # 删除按钮
        self.delete_button = ctk.CTkButton(
            self.property_frame,
            text="删除当前元素",
            command=self.delete_textbox,
            fg_color="red",
            hover_color="darkred",
        )
        self.delete_button.pack(padx=10, pady=(20, 5), fill="x")

        # Excel数据预览
        ctk.CTkLabel(self.property_frame, text="Excel数据预览:", font=ctk.CTkFont(weight="bold")).pack(
            anchor="w", padx=10, pady=(20, 5)
        )
        self.excel_preview = scrolledtext.ScrolledText(self.property_frame, height=6, width=30, state="disabled")
        self.excel_preview.pack(padx=10, pady=5, fill="both", expand=True)

    def draw_a4_grid(self):
        """绘制A4网格线"""
        # 清除现有网格
        self.canvas.delete("grid")

        # 绘制边框
        self.canvas.create_rectangle(0, 0, self.canvas_width, self.canvas_height, outline="gray", width=2, tags="grid")

        # 绘制网格线（每10mm一条）
        grid_mm = 10
        grid_px = grid_mm * self.scale_x

        # 垂直线
        for i in range(1, int(self.A4_WIDTH_MM / grid_mm)):
            x = i * grid_px
            self.canvas.create_line(x, 0, x, self.canvas_height, fill="lightgray", tags="grid")

        # 水平线
        for i in range(1, int(self.A4_HEIGHT_MM / grid_mm)):
            y = i * grid_px
            self.canvas.create_line(0, y, self.canvas_width, y, fill="lightgray", tags="grid")

    def load_background(self):
        """加载背景图片"""
        file_path = filedialog.askopenfilename(
            title="选择背景图片", filetypes=[("图片文件", "*.jpg *.jpeg *.png *.gif *.bmp")]
        )

        if file_path:
            try:
                # 保存原始图片路径
                self.background_image_path = file_path

                # 加载并调整图片尺寸
                image = Image.open(file_path)

                # 等比例缩放到画布大小
                try:
                    # 新版本Pillow
                    image = image.resize((self.canvas_width, self.canvas_height), Image.Resampling.LANCZOS)
                except AttributeError:
                    # 旧版本Pillow兼容
                    image = image.resize((self.canvas_width, self.canvas_height), Image.LANCZOS)

                self.background_image = image

                if IMAGETK_AVAILABLE:
                    try:
                        # 转换为Tkinter可用的格式
                        self.background_photo = ImageTk.PhotoImage(image)

                        # 在画布上显示背景图片
                        self.canvas.delete("background")
                        self.canvas.create_image(0, 0, anchor="nw", image=self.background_photo, tags="background")

                        # 将背景图片置于底层
                        self.canvas.tag_lower("background")

                        print("背景图片加载成功")

                    except Exception as e:
                        # ImageTk转换失败，使用替代方案
                        print(f"ImageTk转换失败: {e}")
                        self._load_background_fallback(image, file_path)
                else:
                    # ImageTk不可用，使用替代方案
                    self._load_background_fallback(image, file_path)

            except Exception as e:
                messagebox.showerror("错误", f"加载背景图片失败: {e}")

    def _load_background_fallback(self, image, file_path):
        """背景图片加载的替代方案"""
        try:
            # 尝试使用Tkinter原生PhotoImage（仅支持PPM/PGM/GIF格式）
            # 先将PIL图片转换为PPM格式
            image_ppm = io.BytesIO()
            image.save(image_ppm, format="PPM")
            image_ppm.seek(0)

            # 创建Tkinter PhotoImage
            self.background_photo = tk.PhotoImage(data=image_ppm.getvalue())

            # 清除现有背景
            self.canvas.delete("background")

            # 在画布上显示背景图片
            self.canvas.create_image(0, 0, anchor="nw", image=self.background_photo, tags="background")

            # 将背景图片置于底层
            self.canvas.tag_lower("background")

            import os

            filename = os.path.basename(file_path)
            print(f"背景图片加载成功 (PPM格式): {filename}")

        except Exception as e:
            print(f"PPM转换也失败: {e}")
            # 最后的备用方案：显示占位符
            self._load_background_placeholder(image, file_path)

    def _load_background_placeholder(self, image, file_path):
        """最终的占位符方案"""
        # 清除现有背景
        self.canvas.delete("background")

        # 在画布上绘制一个带有文件名的占位符
        self.canvas.create_rectangle(
            0, 0, self.canvas_width, self.canvas_height, fill="lightgray", outline="gray", width=2, tags="background"
        )

        # 显示图片信息
        import os

        filename = os.path.basename(file_path)
        info_text = f"背景图片已加载\n{filename}\n{image.size[0]}x{image.size[1]}"

        self.canvas.create_text(
            self.canvas_width // 2,
            self.canvas_height // 2,
            text=info_text,
            anchor="center",
            font=("Arial", 12),
            fill="black",
            tags="background",
        )

        # 将背景置于底层
        self.canvas.tag_lower("background")

        print(f"背景图片加载成功 (占位符显示): {filename}")

    def load_excel(self):
        """加载Excel文件"""
        file_path = filedialog.askopenfilename(title="选择Excel文件", filetypes=[("Excel文件", "*.xlsx *.xls")])

        if file_path:
            try:
                # 读取Excel文件
                self.excel_data = pd.read_excel(file_path)

                # 更新Excel数据预览
                self.update_excel_preview()

                print(f"Excel文件加载成功，共{len(self.excel_data)}行数据")

            except Exception as e:
                messagebox.showerror("错误", f"加载Excel文件失败: {e}")

    def update_excel_preview(self):
        """更新Excel数据预览"""
        if self.excel_data is not None and not self.excel_data.empty:
            # 显示第一行数据和列名
            preview_text = "可用列名:\n"
            for col in self.excel_data.columns:
                preview_text += f"  {{{col}}}\n"

            preview_text += "\n第一行数据:\n"
            first_row = self.excel_data.iloc[0]
            for col, value in first_row.items():
                preview_text += f"  {col}: {value}\n"

            self.excel_preview.config(state="normal")
            self.excel_preview.delete(1.0, tk.END)
            self.excel_preview.insert(1.0, preview_text)
            self.excel_preview.config(state="disabled")

    def add_textbox(self):
        """添加文本框"""
        # 在画布中心添加文本框
        x_mm = self.A4_WIDTH_MM / 2 - 50
        y_mm = self.A4_HEIGHT_MM / 2 - 15

        textbox = TextBox(x_mm, y_mm, text="新文本框\n请编辑内容")
        self.text_boxes.append(textbox)

        self.draw_textbox(textbox)
        self.select_element(textbox)

    def add_imagebox(self):
        """添加图片框"""
        # 在画布中心添加图片框
        x_mm = self.A4_WIDTH_MM / 2 - 15
        y_mm = self.A4_HEIGHT_MM / 2 - 20

        imagebox = ImageBox(x_mm, y_mm)
        self.image_boxes.append(imagebox)

        self.draw_imagebox(imagebox)
        self.select_element(imagebox)

    def add_barcodebox(self):
        """添加条形码框"""
        # 在画布中心添加条形码框（考虑15:4比例的新尺寸）
        x_mm = self.A4_WIDTH_MM / 2 - 30  # 60mm宽度的一半
        y_mm = self.A4_HEIGHT_MM / 2 - 8  # 16mm高度的一半

        barcodebox = BarcodeBox(x_mm, y_mm)
        self.barcode_boxes.append(barcodebox)

        self.draw_barcodebox(barcodebox)
        self.select_element(barcodebox)

    def delete_textbox(self):
        """删除选中的元素"""
        if not self.selected_element:
            messagebox.showwarning("警告", "请先选择要删除的元素")
            return

        # 从画布上删除元素的所有canvas对象
        for canvas_id in self.selected_element.canvas_ids:
            self.canvas.delete(canvas_id)

        # 从相应列表中移除
        if self.selected_element.element_type == "text" and self.selected_element in self.text_boxes:
            self.text_boxes.remove(self.selected_element)
            print("文本框已删除")
        elif self.selected_element.element_type == "image" and self.selected_element in self.image_boxes:
            self.image_boxes.remove(self.selected_element)
            print("图片框已删除")
        elif self.selected_element.element_type == "barcode" and self.selected_element in self.barcode_boxes:
            self.barcode_boxes.remove(self.selected_element)
            print("条形码框已删除")

        # 清空选择
        self.selected_element = None

        # 清空属性面板
        self.clear_property_panel()

    def draw_textbox(self, textbox):
        """绘制文本框"""
        # 删除旧的所有显示对象
        for canvas_id in textbox.canvas_ids:
            self.canvas.delete(canvas_id)
        textbox.canvas_ids.clear()

        # 获取显示文本（替换占位符）
        display_text = self.replace_placeholders(textbox.text)

        # 计算字体大小（直接使用磅值，让Tkinter处理DPI转换）
        # Tkinter会根据系统DPI自动调整字体大小
        font_size = max(8, int(textbox.font_size))

        # 处理多行文本和空格
        lines = display_text.split("\n")
        processed_lines = []

        # 处理每一行的空格（替换为全角空格以保持正确宽度）
        for line in lines:
            # 将连续空格替换为对应数量的全角空格，保持缩进效果
            processed_line = line.replace(" ", "　")  # 替换为全角空格
            processed_lines.append(processed_line)

        # 计算行高像素值
        line_height_px = font_size * textbox.line_height

        # 计算文本框尺寸
        padding = 4
        max_line_width = 0

        # 计算每行的宽度，找出最宽的行
        for line in processed_lines:
            if line.strip():  # 非空行
                temp_text_id = self.canvas.create_text(0, 0, text=line, anchor="nw", font=("Arial", font_size))
                text_bbox = self.canvas.bbox(temp_text_id)
                self.canvas.delete(temp_text_id)

                if text_bbox:
                    line_width = text_bbox[2] - text_bbox[0]
                    max_line_width = max(max_line_width, line_width)

        # 如果没有文本内容，使用默认宽度
        if max_line_width == 0:
            max_line_width = 100

        # 计算文本框总尺寸
        box_width = max_line_width + padding * 2
        box_height = len(processed_lines) * line_height_px + padding * 2

        # 转换坐标
        x_px = textbox.x * self.scale_x
        y_px = textbox.y * self.scale_y

        # 更新文本框的实际尺寸（毫米）
        textbox.width = box_width / self.scale_x
        textbox.height = box_height / self.scale_y

        # 创建文本框背景（透明）
        bg_id = self.canvas.create_rectangle(
            x_px,
            y_px,
            x_px + box_width,
            y_px + box_height,
            fill="",  # 透明背景
            outline="",
            tags=f"textbox_{id(textbox)}",
        )

        # 创建文本框边框
        border_id = self.canvas.create_rectangle(
            x_px,
            y_px,
            x_px + box_width,
            y_px + box_height,
            outline="blue" if textbox.selected else "gray",
            width=2 if textbox.selected else 1,
            fill="",
            tags=f"textbox_{id(textbox)}",
        )

        # 创建每行文本
        text_ids = []
        for i, line in enumerate(processed_lines):
            text_y = y_px + padding + i * line_height_px
            text_id = self.canvas.create_text(
                x_px + padding,
                text_y,
                text=line,
                anchor="nw",
                font=("Arial", font_size),
                fill="black",
                tags=f"textbox_{id(textbox)}",
            )
            text_ids.append(text_id)

        # 存储所有canvas对象ID
        textbox.canvas_ids = [bg_id, border_id] + text_ids

        # 绑定点击事件到所有对象
        for canvas_id in textbox.canvas_ids:
            self.canvas.tag_bind(f"textbox_{id(textbox)}", "<Button-1>", lambda e, tb=textbox: self.select_element(tb))

    def draw_imagebox(self, imagebox):
        """绘制图片框"""
        # 删除旧的所有显示对象
        for canvas_id in imagebox.canvas_ids:
            self.canvas.delete(canvas_id)
        imagebox.canvas_ids.clear()

        # 转换坐标
        x_px = imagebox.x * self.scale_x
        y_px = imagebox.y * self.scale_y
        width_px = imagebox.width * self.scale_x
        height_px = imagebox.height * self.scale_y

        # 尝试加载图片
        image_data = self.find_image_by_ksh(imagebox.ksh_placeholder)

        if image_data:
            try:
                # 如果找到图片，显示实际图片
                # 调整图片尺寸以适应框架
                resized_image = image_data.resize((int(width_px), int(height_px)), Image.Resampling.LANCZOS)

                if IMAGETK_AVAILABLE:
                    try:
                        photo = ImageTk.PhotoImage(resized_image)
                        imagebox.image_data = photo  # 防止垃圾回收

                        image_id = self.canvas.create_image(
                            x_px, y_px, anchor="nw", image=photo, tags=f"imagebox_{id(imagebox)}"
                        )
                        imagebox.canvas_ids.append(image_id)
                    except:
                        # ImageTk失败，使用PPM格式
                        image_ppm = io.BytesIO()
                        resized_image.save(image_ppm, format="PPM")
                        image_ppm.seek(0)
                        photo = tk.PhotoImage(data=image_ppm.getvalue())
                        imagebox.image_data = photo

                        image_id = self.canvas.create_image(
                            x_px, y_px, anchor="nw", image=photo, tags=f"imagebox_{id(imagebox)}"
                        )
                        imagebox.canvas_ids.append(image_id)
                else:
                    # 无法显示图片，显示占位符
                    self._draw_image_placeholder(imagebox, x_px, y_px, width_px, height_px)

            except Exception as e:
                print(f"图片加载失败: {e}")
                self._draw_image_placeholder(imagebox, x_px, y_px, width_px, height_px)
        else:
            # 没有找到图片，显示占位符
            self._draw_image_placeholder(imagebox, x_px, y_px, width_px, height_px)

        # 创建边框
        border_id = self.canvas.create_rectangle(
            x_px,
            y_px,
            x_px + width_px,
            y_px + height_px,
            outline="blue" if imagebox.selected else "gray",
            width=2 if imagebox.selected else 1,
            fill="",
            tags=f"imagebox_{id(imagebox)}",
        )
        imagebox.canvas_ids.append(border_id)

        # 绑定点击事件
        for canvas_id in imagebox.canvas_ids:
            self.canvas.tag_bind(
                f"imagebox_{id(imagebox)}", "<Button-1>", lambda e, ib=imagebox: self.select_element(ib)
            )

    def draw_barcodebox(self, barcodebox):
        """绘制条形码框"""
        # 删除旧的所有显示对象
        for canvas_id in barcodebox.canvas_ids:
            self.canvas.delete(canvas_id)
        barcodebox.canvas_ids.clear()

        # 转换坐标
        x_px = barcodebox.x * self.scale_x
        y_px = barcodebox.y * self.scale_y
        width_px = barcodebox.width * self.scale_x
        height_px = barcodebox.height * self.scale_y

        # 生成条形码图片
        barcode_image = self.generate_barcode_image(barcodebox)

        if barcode_image:
            try:
                # 调整条形码图片尺寸以适应框架
                resized_barcode = barcode_image.resize((int(width_px), int(height_px)), Image.Resampling.LANCZOS)

                if IMAGETK_AVAILABLE:
                    try:
                        photo = ImageTk.PhotoImage(resized_barcode)
                        barcodebox.barcode_image = photo  # 防止垃圾回收

                        image_id = self.canvas.create_image(
                            x_px, y_px, anchor="nw", image=photo, tags=f"barcodebox_{id(barcodebox)}"
                        )
                        barcodebox.canvas_ids.append(image_id)
                    except:
                        # ImageTk失败，使用PPM格式
                        image_ppm = io.BytesIO()
                        resized_barcode.save(image_ppm, format="PPM")
                        image_ppm.seek(0)
                        photo = tk.PhotoImage(data=image_ppm.getvalue())
                        barcodebox.barcode_image = photo

                        image_id = self.canvas.create_image(
                            x_px, y_px, anchor="nw", image=photo, tags=f"barcodebox_{id(barcodebox)}"
                        )
                        barcodebox.canvas_ids.append(image_id)
                else:
                    # 无法显示图片，显示占位符
                    self._draw_barcode_placeholder(barcodebox, x_px, y_px, width_px, height_px)

            except Exception as e:
                print(f"条形码图片加载失败: {e}")
                self._draw_barcode_placeholder(barcodebox, x_px, y_px, width_px, height_px)
        else:
            # 无法生成条形码，显示占位符
            self._draw_barcode_placeholder(barcodebox, x_px, y_px, width_px, height_px)

        # 创建边框
        border_id = self.canvas.create_rectangle(
            x_px,
            y_px,
            x_px + width_px,
            y_px + height_px,
            outline="blue" if barcodebox.selected else "gray",
            width=2 if barcodebox.selected else 1,
            fill="",
            tags=f"barcodebox_{id(barcodebox)}",
        )
        barcodebox.canvas_ids.append(border_id)

        # 绑定点击事件
        for canvas_id in barcodebox.canvas_ids:
            self.canvas.tag_bind(
                f"barcodebox_{id(barcodebox)}", "<Button-1>", lambda e, bb=barcodebox: self.select_element(bb)
            )

    def _draw_image_placeholder(self, imagebox, x_px, y_px, width_px, height_px):
        """绘制图片占位符"""
        # 背景
        bg_id = self.canvas.create_rectangle(
            x_px,
            y_px,
            x_px + width_px,
            y_px + height_px,
            fill="lightgray",
            outline="",
            tags=f"imagebox_{id(imagebox)}",
        )
        imagebox.canvas_ids.append(bg_id)

        # 占位符文字
        text_id = self.canvas.create_text(
            x_px + width_px / 2,
            y_px + height_px / 2,
            text=f"图片\n{imagebox.ksh_placeholder}",
            anchor="center",
            font=("Arial", 10),
            fill="black",
            tags=f"imagebox_{id(imagebox)}",
        )
        imagebox.canvas_ids.append(text_id)

    def _draw_barcode_placeholder(self, barcodebox, x_px, y_px, width_px, height_px):
        """绘制条形码占位符"""
        # 背景
        bg_id = self.canvas.create_rectangle(
            x_px,
            y_px,
            x_px + width_px,
            y_px + height_px,
            fill="lightblue",
            outline="",
            tags=f"barcodebox_{id(barcodebox)}",
        )
        barcodebox.canvas_ids.append(bg_id)

        # 占位符文字
        text_id = self.canvas.create_text(
            x_px + width_px / 2,
            y_px + height_px / 2,
            text=f"条形码\n{barcodebox.text}",
            anchor="center",
            font=("Arial", 10),
            fill="black",
            tags=f"barcodebox_{id(barcodebox)}",
        )
        barcodebox.canvas_ids.append(text_id)

    def generate_barcode_image(self, barcodebox):
        """生成条形码图片"""
        try:
            # 获取显示文本（替换占位符）
            display_text = self.replace_placeholders(barcodebox.text)

            # 如果还包含未替换的占位符，使用原始文本
            if "{" in display_text and "}" in display_text:
                display_text = "123456789"  # 使用默认文本作为预览

            # 确保文本符合条形码类型的要求
            display_text = self.validate_barcode_text(display_text, barcodebox.barcode_type)

            # 获取条形码类别
            barcode_class = barcode.get_barcode_class(barcodebox.barcode_type)

            # 创建ImageWriter，设置透明背景和字体大小
            writer = ImageWriter()
            writer.format = "PNG"  # 使用PNG格式支持透明度

            # 创建条形码
            code = barcode_class(display_text, writer=writer)

            # 生成图片到内存，设置较小的字体大小和黑体字体
            buffer = io.BytesIO()
            options = {
                "write_text": True,  # 显示文本
                "font_size": 10,  # 设置较小的字体大小（默认是14）
                "text_distance": 5.0,  # 文字与条形码的距离，增加距离避免重合
            }
            
            # 尝试设置字体路径
            font_path = self.get_font_path()
            if font_path:
                options["font_path"] = font_path
                
            try:
                code.write(buffer, options)
            except Exception as e:
                print(f"使用自定义字体失败，使用默认字体: {e}")
                # 移除font_path选项，使用默认字体
                if "font_path" in options:
                    del options["font_path"]
                code.write(buffer, options)
            buffer.seek(0)

            # 创建PIL图片
            barcode_image = Image.open(buffer)

            # 转换为RGBA模式以支持透明度
            if barcode_image.mode != "RGBA":
                barcode_image = barcode_image.convert("RGBA")

            # 将白色背景设置为透明
            data = barcode_image.getdata()
            new_data = []
            for item in data:
                # 如果是白色或接近白色的像素，设置为透明
                if len(item) >= 3 and item[0] > 240 and item[1] > 240 and item[2] > 240:
                    new_data.append((255, 255, 255, 0))  # 透明
                else:
                    new_data.append(item)  # 保持原样

            barcode_image.putdata(new_data)
            return barcode_image

        except Exception as e:
            print(f"生成条形码失败: {e}")
            return None

    def validate_barcode_text(self, text, barcode_type):
        """验证并修正条形码文本"""
        # 移除无效字符并截取适当长度
        if barcode_type == "code128":
            # Code128支持大部分字符，只需要确保不为空
            return text if text else "123456789"
        elif barcode_type == "code39":
            # Code39只支持数字、大写字母和一些特殊字符
            valid_chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%"
            validated = "".join(c for c in text.upper() if c in valid_chars)
            return validated if validated else "123456789"
        elif barcode_type == "ean13":
            # EAN13需要12或13位数字
            digits = "".join(c for c in text if c.isdigit())
            if len(digits) >= 12:
                return digits[:12]  # 取前12位，让库自动生成校验位
            else:
                return "123456789012"  # 默认12位数字
        elif barcode_type == "ean8":
            # EAN8需要7或8位数字
            digits = "".join(c for c in text if c.isdigit())
            if len(digits) >= 7:
                return digits[:7]  # 取前7位，让库自动生成校验位
            else:
                return "1234567"  # 默认7位数字
        else:
            return text if text else "123456789"

    def find_image_by_ksh(self, ksh_placeholder):
        """根据ksh占位符查找图片"""
        if self.excel_data is None or self.excel_data.empty:
            return None

        # 获取第一行数据用于预览
        first_row = self.excel_data.iloc[0]

        # 替换占位符获取实际ksh值
        ksh_value = ksh_placeholder
        for col, value in first_row.items():
            placeholder = f"{{{col}}}"
            if placeholder in ksh_value:
                ksh_value = ksh_value.replace(placeholder, str(value))

        # 如果还包含未替换的占位符，返回None
        if "{" in ksh_value and "}" in ksh_value:
            return None

        # 在KSZP文件夹中查找图片
        kszp_folder = "KSZP"
        if not os.path.exists(kszp_folder):
            return None

        # 支持的图片格式
        image_extensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp"]

        for ext in image_extensions:
            image_path = os.path.join(kszp_folder, f"{ksh_value}{ext}")
            if os.path.exists(image_path):
                try:
                    return Image.open(image_path)
                except Exception as e:
                    print(f"无法打开图片 {image_path}: {e}")
                    continue

        return None

    def find_image_by_ksh_for_pdf(self, ksh_placeholder, data_row):
        """根据ksh占位符查找图片（用于PDF生成）"""
        # 替换占位符获取实际ksh值
        ksh_value = ksh_placeholder
        for col, value in data_row.items():
            placeholder = f"{{{col}}}"
            if placeholder in ksh_value:
                ksh_value = ksh_value.replace(placeholder, str(value))

        # 如果还包含未替换的占位符，返回None
        if "{" in ksh_value and "}" in ksh_value:
            return None

        # 在KSZP文件夹中查找图片
        kszp_folder = "KSZP"
        if not os.path.exists(kszp_folder):
            return None

        # 支持的图片格式
        image_extensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp"]

        for ext in image_extensions:
            image_path = os.path.join(kszp_folder, f"{ksh_value}{ext}")
            if os.path.exists(image_path):
                try:
                    return Image.open(image_path)
                except Exception as e:
                    print(f"无法打开图片 {image_path}: {e}")
                    continue

        return None

    def replace_placeholders(self, text):
        """替换文本中的占位符"""
        if self.excel_data is None or self.excel_data.empty:
            return text

        result = text
        first_row = self.excel_data.iloc[0]

        for col, value in first_row.items():
            placeholder = f"{{{col}}}"
            if placeholder in result:
                result = result.replace(placeholder, str(value))

        return result

    def select_element(self, element):
        """选择元素（文本框或图片框）"""
        # 取消之前的选择
        if self.selected_element:
            self.selected_element.selected = False
            if self.selected_element.element_type == "text":
                self.draw_textbox(self.selected_element)
            elif self.selected_element.element_type == "image":
                self.draw_imagebox(self.selected_element)
            elif self.selected_element.element_type == "barcode":
                self.draw_barcodebox(self.selected_element)

        # 选择新的元素
        self.selected_element = element
        element.selected = True
        if element.element_type == "text":
            self.draw_textbox(element)
        elif element.element_type == "image":
            self.draw_imagebox(element)
        elif element.element_type == "barcode":
            self.draw_barcodebox(element)

        # 更新属性面板
        self.update_property_panel()

    def update_property_panel(self):
        """更新属性面板"""
        if self.selected_element and self.selected_element.element_type == "text":
            tb = self.selected_element

            # 更新文本内容
            self.text_entry.delete(1.0, tk.END)
            self.text_entry.insert(1.0, tb.text)

            # 更新字号
            self.fontsize_var.set(tb.font_size_name)

            # 更新行高
            self.lineheight_var.set(str(tb.line_height))

            # 更新位置
            self.position_label.configure(text=f"X: {tb.x:.1f}, Y: {tb.y:.1f}")
        elif self.selected_element and self.selected_element.element_type == "image":
            ib = self.selected_element
            # 对于图片框，暂时只显示位置信息
            # 更新位置
            self.position_label.configure(text=f"X: {ib.x:.1f}, Y: {ib.y:.1f} (图片框)")
        elif self.selected_element and self.selected_element.element_type == "barcode":
            bb = self.selected_element

            # 更新文本内容（条形码内容）
            self.text_entry.delete(1.0, tk.END)
            self.text_entry.insert(1.0, bb.text)

            # 更新条形码类型
            self.barcode_type_var.set(bb.barcode_type)

            # 更新条形码尺寸
            self.barcode_width_var.set(str(bb.width))
            self.barcode_height_var.set(str(bb.height))

            # 更新位置
            self.position_label.configure(
                text=f"X: {bb.x:.1f}, Y: {bb.y:.1f} (条形码 {bb.width:.1f}×{bb.height:.1f}mm)"
            )

    def clear_property_panel(self):
        """清空属性面板"""
        # 清空文本内容
        self.text_entry.delete(1.0, tk.END)

        # 重置字号
        self.fontsize_var.set("小四")

        # 重置行高
        self.lineheight_var.set("1.2")

        # 重置位置
        self.position_label.configure(text="X: 0, Y: 0")

    def on_text_change(self, event=None):
        """文本内容改变"""
        if self.selected_element and self.selected_element.element_type == "text":
            self.selected_element.text = self.text_entry.get(1.0, tk.END).strip()
            self.draw_textbox(self.selected_element)
        elif self.selected_element and self.selected_element.element_type == "barcode":
            self.selected_element.text = self.text_entry.get(1.0, tk.END).strip()
            self.draw_barcodebox(self.selected_element)

    def on_fontsize_change(self, font_size_name=None):
        """字号改变"""
        if self.selected_element and self.selected_element.element_type == "text":
            if font_size_name is None:
                font_size_name = self.fontsize_var.get()

            if font_size_name in CHINESE_FONT_SIZES:
                self.selected_element.font_size_name = font_size_name
                self.selected_element.font_size = CHINESE_FONT_SIZES[font_size_name]
                self.draw_textbox(self.selected_element)

    def on_lineheight_change(self, event=None):
        """行高改变"""
        if self.selected_element and self.selected_element.element_type == "text":
            try:
                line_height = float(self.lineheight_var.get())
                if line_height > 0:
                    self.selected_element.line_height = line_height
                    self.draw_textbox(self.selected_element)
            except ValueError:
                pass

    def on_barcode_type_change(self, barcode_type=None):
        """条形码类型改变"""
        if self.selected_element and self.selected_element.element_type == "barcode":
            if barcode_type is None:
                barcode_type = self.barcode_type_var.get()

            self.selected_element.barcode_type = barcode_type
            self.draw_barcodebox(self.selected_element)

    def on_barcode_width_change(self, event=None):
        """条形码宽度改变，自动调整高度保持15:4比例"""
        if self.selected_element and self.selected_element.element_type == "barcode":
            try:
                width = float(self.barcode_width_var.get())
                if width > 0:
                    # 按15:4比例自动计算高度
                    height = width * 4 / 15
                    
                    # 更新高度输入框的值（不会触发高度变化事件）
                    self.barcode_height_var.set(f"{height:.1f}")
                    
                    # 更新条形码元素
                    self.selected_element.width = width
                    self.selected_element.height = height
                    self.draw_barcodebox(self.selected_element)

                    # 更新位置显示
                    self.position_label.configure(
                        text=f"X: {self.selected_element.x:.1f}, Y: {self.selected_element.y:.1f} (条形码 {width:.1f}×{height:.1f}mm)"
                    )
            except ValueError:
                pass
    
    def on_barcode_height_change(self, event=None):
        """条形码高度改变，只更新高度不影响宽度"""
        if self.selected_element and self.selected_element.element_type == "barcode":
            try:
                width = float(self.barcode_width_var.get())
                height = float(self.barcode_height_var.get())

                if width > 0 and height > 0:
                    self.selected_element.width = width
                    self.selected_element.height = height
                    self.draw_barcodebox(self.selected_element)

                    # 更新位置显示
                    self.position_label.configure(
                        text=f"X: {self.selected_element.x:.1f}, Y: {self.selected_element.y:.1f} (条形码 {width:.1f}×{height:.1f}mm)"
                    )
            except ValueError:
                pass

    def reset_barcode_ratio(self):
        """重置条形码为15:4比例"""
        if self.selected_element and self.selected_element.element_type == "barcode":
            # 基于当前宽度计算15:4比例的高度，或者使用默认尺寸
            try:
                current_width = float(self.barcode_width_var.get())
                new_height = current_width * 4 / 15  # 15:4比例

                self.barcode_height_var.set(f"{new_height:.1f}")
                self.selected_element.height = new_height
                self.draw_barcodebox(self.selected_element)

                # 更新位置显示
                self.position_label.configure(
                    text=f"X: {self.selected_element.x:.1f}, Y: {self.selected_element.y:.1f} (条形码 {current_width:.1f}×{new_height:.1f}mm)"
                )
            except ValueError:
                # 如果当前宽度无效，使用默认15:4比例尺寸
                self.barcode_width_var.set("60")
                self.barcode_height_var.set("16")
                self.selected_element.width = 60
                self.selected_element.height = 16
                self.draw_barcodebox(self.selected_element)

                # 更新位置显示
                self.position_label.configure(
                    text=f"X: {self.selected_element.x:.1f}, Y: {self.selected_element.y:.1f} (条形码 60.0×16.0mm)"
                )

    def on_canvas_click(self, event):
        """画布点击事件"""
        # 转换坐标
        x_mm = event.x / self.scale_x
        y_mm = event.y / self.scale_y

        # 检查是否点击了文本框或图片框
        clicked_element = None

        # 检查文本框
        for tb in self.text_boxes:
            if tb.x <= x_mm <= tb.x + tb.width and tb.y <= y_mm <= tb.y + tb.height:
                clicked_element = tb
                break

        # 如果没有点击文本框，检查图片框
        if not clicked_element:
            for ib in self.image_boxes:
                if ib.x <= x_mm <= ib.x + ib.width and ib.y <= y_mm <= ib.y + ib.height:
                    clicked_element = ib
                    break

        # 如果没有点击图片框，检查条形码框
        if not clicked_element:
            for bb in self.barcode_boxes:
                if bb.x <= x_mm <= bb.x + bb.width and bb.y <= y_mm <= bb.y + bb.height:
                    clicked_element = bb
                    break

        if clicked_element:
            self.select_element(clicked_element)
            # 记录拖拽起始位置
            self.drag_data["x"] = event.x
            self.drag_data["y"] = event.y
        else:
            # 取消选择
            if self.selected_element:
                self.selected_element.selected = False
                if self.selected_element.element_type == "text":
                    self.draw_textbox(self.selected_element)
                elif self.selected_element.element_type == "image":
                    self.draw_imagebox(self.selected_element)
                elif self.selected_element.element_type == "barcode":
                    self.draw_barcodebox(self.selected_element)
                self.selected_element = None

    def on_canvas_drag(self, event):
        """画布拖拽事件"""
        if self.selected_element:
            # 计算移动距离
            dx_px = event.x - self.drag_data["x"]
            dy_px = event.y - self.drag_data["y"]

            # 转换为毫米
            dx_mm = dx_px / self.scale_x
            dy_mm = dy_px / self.scale_y

            # 更新元素位置
            self.selected_element.x += dx_mm
            self.selected_element.y += dy_mm

            # 限制在画布范围内
            self.selected_element.x = max(
                0, min(self.selected_element.x, self.A4_WIDTH_MM - self.selected_element.width)
            )
            self.selected_element.y = max(
                0, min(self.selected_element.y, self.A4_HEIGHT_MM - self.selected_element.height)
            )

            # 重绘元素
            if self.selected_element.element_type == "text":
                self.draw_textbox(self.selected_element)
            elif self.selected_element.element_type == "image":
                self.draw_imagebox(self.selected_element)
            elif self.selected_element.element_type == "barcode":
                self.draw_barcodebox(self.selected_element)

            # 更新位置显示
            self.position_label.configure(text=f"X: {self.selected_element.x:.1f}, Y: {self.selected_element.y:.1f}")

            # 更新拖拽起始位置
            self.drag_data["x"] = event.x
            self.drag_data["y"] = event.y

    def on_canvas_release(self, event):
        """画布释放事件"""
        pass

    def preview_pdf(self):
        """预览PDF"""
        if not self.text_boxes and not self.image_boxes and not self.barcode_boxes:
            messagebox.showwarning("警告", "请先添加文本框、图片或条形码")
            return

        if self.excel_data is None or self.excel_data.empty:
            messagebox.showwarning("警告", "请先加载Excel数据")
            return

        try:
            # 确保output文件夹存在
            os.makedirs("output", exist_ok=True)

            # 生成预览PDF
            buffer = io.BytesIO()
            self.generate_pdf_page(buffer, self.excel_data.iloc[0])

            # 保存预览文件到output文件夹
            preview_path = os.path.join("output", "preview.pdf")
            with open(preview_path, "wb") as f:
                f.write(buffer.getvalue())

            messagebox.showinfo("预览完成", f"PDF预览已生成: {preview_path}")

            # 尝试打开预览文件
            try:
                os.system(f"open '{preview_path}'")  # macOS
            except:
                try:
                    os.system(f"start '{preview_path}'")  # Windows
                except:
                    pass

        except Exception as e:
            messagebox.showerror("错误", f"生成预览PDF失败: {e}")

    def export_pdf(self):
        """导出PDF"""
        if not self.text_boxes and not self.image_boxes and not self.barcode_boxes:
            messagebox.showwarning("警告", "请先添加文本框、图片或条形码")
            return

        if self.excel_data is None or self.excel_data.empty:
            messagebox.showwarning("警告", "请先加载Excel数据")
            return

        # 确保output文件夹存在
        os.makedirs("output", exist_ok=True)

        # 自动生成文件名：{datetime}_{人数}人.pdf
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        total_people = len(self.excel_data)
        filename = f"{current_time}_{total_people}人.pdf"
        file_path = os.path.join("output", filename)

        try:
            # 生成PDF
            self.generate_batch_pdf(file_path)
            messagebox.showinfo("导出完成", f"PDF文件已导出: {file_path}")

        except Exception as e:
            messagebox.showerror("错误", f"导出PDF失败: {e}")

    def generate_pdf_page(self, buffer, data_row):
        """生成单页PDF"""
        c = canvas.Canvas(buffer, pagesize=(self.A4_WIDTH_MM * mm, self.A4_HEIGHT_MM * mm))

        # 绘制背景图片（预览使用缩放版本以加速生成）
        if self.background_image:
            try:
                # 预览使用已缩放的图片，速度更快
                temp_bg_path = "temp_background_preview.jpg"
                # 为预览生成适中分辨率的背景图片
                bg_resized = self.background_image.resize(
                    (int(self.A4_WIDTH_MM * mm * 72 / 25.4), int(self.A4_HEIGHT_MM * mm * 72 / 25.4)),
                    Image.Resampling.LANCZOS,
                )
                bg_resized.save(temp_bg_path, "JPEG", quality=85)  # 适中的质量以平衡速度和效果
                c.drawImage(temp_bg_path, 0, 0, width=self.A4_WIDTH_MM * mm, height=self.A4_HEIGHT_MM * mm)
                os.remove(temp_bg_path)
            except Exception as e:
                print(f"预览背景图片添加失败: {e}")

        # 绘制图片框
        for ib in self.image_boxes:
            image_data = self.find_image_by_ksh_for_pdf(ib.ksh_placeholder, data_row)
            if image_data:
                try:
                    # 保存为临时文件
                    temp_img_path = f"temp_image_{id(ib)}.jpg"
                    # 转换为RGB模式以支持JPEG保存
                    if image_data.mode != "RGB":
                        image_data = image_data.convert("RGB")
                    image_data.save(temp_img_path, "JPEG")

                    # 转换坐标（PDF坐标系原点在左下角）
                    x_pdf = ib.x * mm
                    y_pdf = (self.A4_HEIGHT_MM - ib.y - ib.height) * mm
                    width_pdf = ib.width * mm
                    height_pdf = ib.height * mm

                    c.drawImage(temp_img_path, x_pdf, y_pdf, width=width_pdf, height=height_pdf)

                    # 删除临时文件
                    os.remove(temp_img_path)
                except Exception as e:
                    print(f"图片添加失败: {e}")

        # 绘制条形码框
        for bb in self.barcode_boxes:
            try:
                # 处理条形码内容，替换占位符
                barcode_text = bb.text
                for col, value in data_row.items():
                    placeholder = f"{{{col}}}"
                    barcode_text = barcode_text.replace(placeholder, str(value))

                # 验证并修正条形码文本
                barcode_text = self.validate_barcode_text(barcode_text, bb.barcode_type)

                # 生成条形码图片
                barcode_class = barcode.get_barcode_class(bb.barcode_type)
                writer = ImageWriter()
                writer.format = "PNG"
                code = barcode_class(barcode_text, writer=writer)

                # 保存为临时文件
                temp_barcode_path = f"temp_barcode_{id(bb)}.png"

                # 通过内存缓冲区生成并保存条形码图片
                buffer = io.BytesIO()
                options = {
                    "write_text": True,  # 显示文本
                    "font_size": 10,  # 设置较小的字体大小
                    "text_distance": 5.0,  # 文字与条形码的距离，增加距离避免重合
                }
                
                # 尝试设置字体路径
                font_path = self.get_font_path()
                if font_path:
                    options["font_path"] = font_path
                    
                try:
                    code.write(buffer, options)
                except Exception as e:
                    print(f"使用自定义字体失败，使用默认字体: {e}")
                    # 移除font_path选项，使用默认字体
                    if "font_path" in options:
                        del options["font_path"]
                    code.write(buffer, options)
                buffer.seek(0)
                barcode_img = Image.open(buffer)

                # 转换为RGBA模式以支持透明度
                if barcode_img.mode != "RGBA":
                    barcode_img = barcode_img.convert("RGBA")

                # 将白色背景设置为透明
                data = barcode_img.getdata()
                new_data = []
                for item in data:
                    # 如果是白色或接近白色的像素，设置为透明
                    if len(item) >= 3 and item[0] > 240 and item[1] > 240 and item[2] > 240:
                        new_data.append((255, 255, 255, 0))  # 透明
                    else:
                        new_data.append(item)  # 保持原样

                barcode_img.putdata(new_data)
                barcode_img.save(temp_barcode_path, "PNG")

                # 确保文件已创建
                if os.path.exists(temp_barcode_path):
                    # 转换坐标（PDF坐标系原点在左下角）
                    x_pdf = bb.x * mm
                    y_pdf = (self.A4_HEIGHT_MM - bb.y - bb.height) * mm
                    width_pdf = bb.width * mm
                    height_pdf = bb.height * mm

                    # 使用mask参数处理透明背景，[250,255,250,255,250,255]会将白色像素设为透明
                    c.drawImage(
                        temp_barcode_path,
                        x_pdf,
                        y_pdf,
                        width=width_pdf,
                        height=height_pdf,
                        mask=[250, 255, 250, 255, 250, 255],
                    )

                    # 删除临时文件
                    os.remove(temp_barcode_path)
                else:
                    print(f"条形码文件创建失败: {temp_barcode_path}")

            except Exception as e:
                print(f"条形码添加失败: {e}")
                import traceback

                traceback.print_exc()

        # 绘制文本框
        for tb in self.text_boxes:
            # 处理文本内容，替换占位符
            text = tb.text
            for col, value in data_row.items():
                placeholder = f"{{{col}}}"
                text = text.replace(placeholder, str(value))

            # 转换坐标（PDF坐标系原点在左下角）
            x_pdf = tb.x * mm
            y_pdf = (self.A4_HEIGHT_MM - tb.y - tb.height) * mm

            # 设置字体
            if self.font_registered:
                c.setFont("SimHei", tb.font_size)
            else:
                c.setFont("Helvetica", tb.font_size)

            # 绘制文本（支持多行和正确的空格处理）
            lines = text.split("\n")
            line_height_pt = tb.font_size * tb.line_height

            for i, line in enumerate(lines):
                # 处理空格：将半角空格替换为全角空格，保持与预览界面一致
                processed_line = line.replace(" ", "　")  # 替换为全角空格
                y_line = y_pdf + tb.height * mm - (i + 1) * line_height_pt
                c.drawString(x_pdf, y_line, processed_line)

        c.save()

    def generate_batch_pdf(self, file_path):
        """生成批量PDF"""
        c = canvas.Canvas(file_path, pagesize=(self.A4_WIDTH_MM * mm, self.A4_HEIGHT_MM * mm))

        for index, row in self.excel_data.iterrows():
            # 绘制背景图片
            if self.background_image_path and os.path.exists(self.background_image_path):
                try:
                    # 直接使用原始背景图片，保持最佳质量
                    c.drawImage(
                        self.background_image_path, 0, 0, width=self.A4_WIDTH_MM * mm, height=self.A4_HEIGHT_MM * mm
                    )
                except Exception as e:
                    print(f"背景图片添加失败: {e}")
                    # 如果原始路径失败，使用缩放后的图片作为备选
                    if self.background_image:
                        try:
                            temp_bg_path = f"temp_background_{index}.jpg"
                            bg_resized = self.background_image.resize(
                                (int(self.A4_WIDTH_MM * mm * 72 / 25.4), int(self.A4_HEIGHT_MM * mm * 72 / 25.4)),
                                Image.Resampling.LANCZOS,
                            )
                            bg_resized.save(temp_bg_path, "JPEG")
                            c.drawImage(temp_bg_path, 0, 0, width=self.A4_WIDTH_MM * mm, height=self.A4_HEIGHT_MM * mm)
                            os.remove(temp_bg_path)
                        except Exception as e2:
                            print(f"备选背景图片添加也失败: {e2}")

            # 绘制图片框
            for ib in self.image_boxes:
                image_data = self.find_image_by_ksh_for_pdf(ib.ksh_placeholder, row)
                if image_data:
                    try:
                        # 保存为临时文件
                        temp_img_path = f"temp_image_{id(ib)}_{index}.jpg"
                        # 转换为RGB模式以支持JPEG保存
                        if image_data.mode != "RGB":
                            image_data = image_data.convert("RGB")
                        image_data.save(temp_img_path, "JPEG")

                        # 转换坐标（PDF坐标系原点在左下角）
                        x_pdf = ib.x * mm
                        y_pdf = (self.A4_HEIGHT_MM - ib.y - ib.height) * mm
                        width_pdf = ib.width * mm
                        height_pdf = ib.height * mm

                        c.drawImage(temp_img_path, x_pdf, y_pdf, width=width_pdf, height=height_pdf)

                        # 删除临时文件
                        os.remove(temp_img_path)
                    except Exception as e:
                        print(f"图片添加失败: {e}")

            # 绘制条形码框
            for bb in self.barcode_boxes:
                try:
                    # 处理条形码内容，替换占位符
                    barcode_text = bb.text
                    for col, value in row.items():
                        placeholder = f"{{{col}}}"
                        barcode_text = barcode_text.replace(placeholder, str(value))

                    # 验证并修正条形码文本
                    barcode_text = self.validate_barcode_text(barcode_text, bb.barcode_type)

                    # 生成条形码图片
                    barcode_class = barcode.get_barcode_class(bb.barcode_type)
                    writer = ImageWriter()
                    writer.format = "PNG"
                    code = barcode_class(barcode_text, writer=writer)

                    # 保存为临时文件
                    temp_barcode_path = f"temp_barcode_{id(bb)}_{index}.png"

                    # 通过内存缓冲区生成并保存条形码图片
                    buffer = io.BytesIO()
                    options = {
                        "write_text": True,  # 显示文本
                        "font_size": 10,  # 设置较小的字体大小
                        "text_distance": 5.0,  # 文字与条形码的距离，增加距离避免重合
                    }
                    
                    # 尝试设置字体路径
                    font_path = self.get_font_path()
                    if font_path:
                        options["font_path"] = font_path
                        
                    try:
                        code.write(buffer, options)
                    except Exception as e:
                        print(f"使用自定义字体失败，使用默认字体: {e}")
                        # 移除font_path选项，使用默认字体
                        if "font_path" in options:
                            del options["font_path"]
                        code.write(buffer, options)
                    buffer.seek(0)
                    barcode_img = Image.open(buffer)

                    # 转换为RGBA模式以支持透明度
                    if barcode_img.mode != "RGBA":
                        barcode_img = barcode_img.convert("RGBA")

                    # 将白色背景设置为透明
                    data = barcode_img.getdata()
                    new_data = []
                    for item in data:
                        # 如果是白色或接近白色的像素，设置为透明
                        if len(item) >= 3 and item[0] > 240 and item[1] > 240 and item[2] > 240:
                            new_data.append((255, 255, 255, 0))  # 透明
                        else:
                            new_data.append(item)  # 保持原样

                    barcode_img.putdata(new_data)
                    barcode_img.save(temp_barcode_path, "PNG")

                    # 确保文件已创建
                    if os.path.exists(temp_barcode_path):
                        # 转换坐标（PDF坐标系原点在左下角）
                        x_pdf = bb.x * mm
                        y_pdf = (self.A4_HEIGHT_MM - bb.y - bb.height) * mm
                        width_pdf = bb.width * mm
                        height_pdf = bb.height * mm

                        # 使用mask参数处理透明背景，[250,255,250,255,250,255]会将白色像素设为透明
                        c.drawImage(
                            temp_barcode_path,
                            x_pdf,
                            y_pdf,
                            width=width_pdf,
                            height=height_pdf,
                            mask=[250, 255, 250, 255, 250, 255],
                        )

                        # 删除临时文件
                        os.remove(temp_barcode_path)
                    else:
                        print(f"条形码文件创建失败: {temp_barcode_path}")

                except Exception as e:
                    print(f"条形码添加失败: {e}")
                    import traceback

                    traceback.print_exc()

            # 绘制文本框
            for tb in self.text_boxes:
                # 处理文本内容，替换占位符
                text = tb.text
                for col, value in row.items():
                    placeholder = f"{{{col}}}"
                    text = text.replace(placeholder, str(value))

                # 转换坐标（PDF坐标系原点在左下角）
                x_pdf = tb.x * mm
                y_pdf = (self.A4_HEIGHT_MM - tb.y - tb.height) * mm

                # 设置字体
                if self.font_registered:
                    c.setFont("SimHei", tb.font_size)
                else:
                    c.setFont("Helvetica", tb.font_size)

                # 绘制文本（支持多行和正确的空格处理）
                lines = text.split("\n")
                line_height_pt = tb.font_size * tb.line_height

                for i, line in enumerate(lines):
                    # 处理空格：将半角空格替换为全角空格，保持与预览界面一致
                    processed_line = line.replace(" ", "　")  # 替换为全角空格
                    y_line = y_pdf + tb.height * mm - (i + 1) * line_height_pt
                    c.drawString(x_pdf, y_line, processed_line)

            # 结束当前页，准备下一页
            if index < len(self.excel_data.index) - 1:
                c.showPage()

        c.save()

    def run(self):
        """运行应用"""
        self.root.mainloop()


def main():
    """主程序入口"""
    try:
        app = PDFGenerator()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
